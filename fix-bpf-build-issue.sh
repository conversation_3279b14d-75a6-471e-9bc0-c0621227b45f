#!/bin/bash

# Quick fix for BPF build issue
# This script disables CONFIG_BPF_PRELOAD to resolve cross-compilation problems

set -e

echo "BPF Build Issue Quick Fix"
echo "========================="
echo ""

# Check if we're in the right directory
if [[ ! -f "scripts/build-kernel.sh" ]]; then
    echo "Error: Please run this script from the ubuntu-rockchip root directory"
    exit 1
fi

# Check if kernel config exists
CONFIG_FILE="build/linux-rockchip/debian/build/build-rockchip/.config"
if [[ ! -f "$CONFIG_FILE" ]]; then
    echo "Error: Kernel config file not found at $CONFIG_FILE"
    echo "Please run the kernel build script first to generate the config"
    exit 1
fi

echo "Found kernel config: $CONFIG_FILE"
echo ""

# Check current BPF_PRELOAD settings
echo "Current BPF_PRELOAD configuration:"
grep -E "CONFIG_BPF_PRELOAD" "$CONFIG_FILE" || echo "No BPF_PRELOAD config found"
echo ""

# Create backup
BACKUP_FILE="${CONFIG_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
cp "$CONFIG_FILE" "$BACKUP_FILE"
echo "Backup created: $BACKUP_FILE"

# Disable BPF_PRELOAD options
echo "Disabling CONFIG_BPF_PRELOAD to fix cross-compilation issues..."

# Replace or add CONFIG_BPF_PRELOAD=n
if grep -q "^CONFIG_BPF_PRELOAD=" "$CONFIG_FILE"; then
    sed -i 's/^CONFIG_BPF_PRELOAD=.*/# CONFIG_BPF_PRELOAD is not set/' "$CONFIG_FILE"
else
    echo "# CONFIG_BPF_PRELOAD is not set" >> "$CONFIG_FILE"
fi

# Replace or add CONFIG_BPF_PRELOAD_UMD=n
if grep -q "^CONFIG_BPF_PRELOAD_UMD=" "$CONFIG_FILE"; then
    sed -i 's/^CONFIG_BPF_PRELOAD_UMD=.*/# CONFIG_BPF_PRELOAD_UMD is not set/' "$CONFIG_FILE"
else
    echo "# CONFIG_BPF_PRELOAD_UMD is not set" >> "$CONFIG_FILE"
fi

echo "✓ BPF_PRELOAD options disabled"
echo ""

# Show the changes
echo "Updated BPF_PRELOAD configuration:"
grep -E "CONFIG_BPF_PRELOAD" "$CONFIG_FILE" || echo "BPF_PRELOAD options are now disabled"
echo ""

# Verify other important BPF options are still enabled
echo "Verifying core BPF options are still enabled:"
CORE_BPF_OPTIONS=("CONFIG_BPF=y" "CONFIG_BPF_SYSCALL=y" "CONFIG_BPF_JIT=y" "CONFIG_DEBUG_INFO_BTF=y")

for option in "${CORE_BPF_OPTIONS[@]}"; do
    config_name=$(echo "$option" | cut -d'=' -f1)
    if grep -q "^${option}" "$CONFIG_FILE"; then
        echo "✓ $option"
    else
        echo "⚠ $config_name not found or not set correctly"
    fi
done

echo ""
echo "Fix applied successfully!"
echo ""
echo "Next steps:"
echo "1. Resume the kernel build:"
echo "   sudo SUITE=jammy ./scripts/build-kernel.sh"
echo ""
echo "2. If you need to restore the original config:"
echo "   cp $BACKUP_FILE $CONFIG_FILE"
echo ""

echo "Note: CONFIG_BPF_PRELOAD is not essential for core eBPF functionality."
echo "This change only affects the kernel's ability to preload certain BPF programs"
echo "at boot time, which is rarely needed for most eBPF use cases."
