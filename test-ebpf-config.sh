#!/bin/bash

# Simple test script to demonstrate the eBPF configuration script
# This script runs the eBPF configuration script in dry-run mode

set -e

echo "eBPF Kernel Configuration Test"
echo "=============================="
echo ""

# Check if we're in the right directory
if [[ ! -f "scripts/enable-ebpf-kernel-config.sh" ]]; then
    echo "Error: Please run this script from the ubuntu-rockchip root directory"
    exit 1
fi

# Check if kernel source exists
if [[ ! -d "build/linux-rockchip" ]]; then
    echo "Error: Kernel source not found. Please run build-kernel.sh first:"
    echo "  sudo SUITE=jammy ./scripts/build-kernel.sh"
    exit 1
fi

echo "Testing eBPF configuration script..."
echo ""

# Test help function (doesn't require root)
echo "1. Testing help function:"
./scripts/enable-ebpf-kernel-config.sh --help
echo ""

# Test with specific config file in dry-run mode
echo "2. Testing with existing kernel config (dry-run mode):"
echo "   This will show what eBPF options are already enabled and what needs to be changed."
echo ""

# Find the actual config file
CONFIG_FILE=""
if [[ -f "build/linux-rockchip/debian/build/build-rockchip/.config" ]]; then
    CONFIG_FILE="build/linux-rockchip/debian/build/build-rockchip/.config"
elif [[ -f "build/linux-rockchip/.config" ]]; then
    CONFIG_FILE="build/linux-rockchip/.config"
else
    echo "Warning: No kernel config file found. The script will try to auto-detect."
fi

if [[ -n "$CONFIG_FILE" ]]; then
    echo "   Using config file: $CONFIG_FILE"
    echo ""
    
    # Check current BPF-related options
    echo "3. Current BPF-related options in kernel config:"
    echo "   (These are the options currently set in the kernel configuration)"
    echo ""
    
    if grep -q "CONFIG_BPF" "$CONFIG_FILE"; then
        echo "   Found BPF options:"
        grep "CONFIG_BPF" "$CONFIG_FILE" | head -10
        echo "   ... (showing first 10 matches)"
    else
        echo "   No BPF options found in current config"
    fi
    echo ""
    
    if grep -q "CONFIG_DEBUG_INFO_BTF" "$CONFIG_FILE"; then
        echo "   Found BTF options:"
        grep "CONFIG_DEBUG_INFO_BTF" "$CONFIG_FILE"
    else
        echo "   No BTF options found in current config"
    fi
    echo ""
    
    if grep -q "CONFIG_CGROUP" "$CONFIG_FILE"; then
        echo "   Found CGROUP options (first 10):"
        grep "CONFIG_CGROUP" "$CONFIG_FILE" | head -10
        echo "   ... (showing first 10 matches)"
    else
        echo "   No CGROUP options found in current config"
    fi
    echo ""
fi

echo "4. Instructions for running the eBPF configuration script:"
echo ""
echo "   To see what changes would be made (dry-run):"
echo "   sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --dry-run --verbose"
echo ""
echo "   To apply the changes:"
echo "   sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --verbose"
echo ""
echo "   To rebuild the kernel after configuration changes:"
echo "   sudo SUITE=jammy ./scripts/build-kernel.sh"
echo ""

echo "5. Verification after kernel rebuild and reboot:"
echo ""
echo "   Check if BTF is available:"
echo "   ls -la /sys/kernel/btf/vmlinux"
echo ""
echo "   Check kernel config:"
echo "   zcat /proc/config.gz | grep -E 'CONFIG_BPF|CONFIG_DEBUG_INFO_BTF'"
echo ""
echo "   Test basic eBPF functionality (if bpftrace is installed):"
echo "   sudo bpftrace -e 'BEGIN { printf(\"eBPF is working!\\n\"); exit(); }'"
echo ""

echo "Test completed successfully!"
echo ""
echo "Note: This test script only shows the current configuration and provides"
echo "      instructions. To actually modify the kernel configuration, run the"
echo "      enable-ebpf-kernel-config.sh script with appropriate permissions."
