#!/bin/bash

# Enhanced build script with eBPF support
# This script demonstrates how to integrate eBPF configuration into the build process

set -eE 
trap 'echo Error: in $0 on line $LINENO' ERR

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Build Ubuntu Rockchip kernel with eBPF support enabled"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -s, --suite SUITE   Ubuntu suite (jammy, noble, oracular, plucky)"
    echo "  -b, --board BOARD   Target board"
    echo "  -f, --flavor FLAVOR Flavor (server, desktop)"
    echo "  --skip-ebpf         Skip eBPF configuration step"
    echo "  --ebpf-only         Only configure eBPF, don't build"
    echo "  --dry-run           Show what eBPF changes would be made"
    echo "  -v, --verbose       Enable verbose output"
    echo ""
    echo "Examples:"
    echo "  $0 -s jammy -b rock-5b -f server"
    echo "  $0 --ebpf-only -s jammy --dry-run"
    echo "  $0 -s noble -b orangepi-5 -f desktop --verbose"
}

# Default values
SUITE=""
BOARD=""
FLAVOR=""
SKIP_EBPF=false
EBPF_ONLY=false
DRY_RUN=false
VERBOSE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -s|--suite)
            SUITE="$2"
            shift 2
            ;;
        -b|--board)
            BOARD="$2"
            shift 2
            ;;
        -f|--flavor)
            FLAVOR="$2"
            shift 2
            ;;
        --skip-ebpf)
            SKIP_EBPF=true
            shift
            ;;
        --ebpf-only)
            EBPF_ONLY=true
            shift
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check if running as root
if [ "$(id -u)" -ne 0 ]; then 
    echo "Please run as root"
    exit 1
fi

# Validate required parameters
if [[ -z "$SUITE" ]]; then
    echo "Error: SUITE is required"
    usage
    exit 1
fi

if [[ "$EBPF_ONLY" = false ]]; then
    if [[ -z "$BOARD" || -z "$FLAVOR" ]]; then
        echo "Error: BOARD and FLAVOR are required for full build"
        usage
        exit 1
    fi
fi

# Verbose logging function
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo "[VERBOSE] $*"
    fi
}

echo "Ubuntu Rockchip Build with eBPF Support"
echo "======================================="
echo "Suite: $SUITE"
if [[ "$EBPF_ONLY" = false ]]; then
    echo "Board: $BOARD"
    echo "Flavor: $FLAVOR"
fi
echo "eBPF Configuration: $([ "$SKIP_EBPF" = true ] && echo "SKIPPED" || echo "ENABLED")"
echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "BUILD")"
echo ""

# Export environment variables
export SUITE
if [[ "$EBPF_ONLY" = false ]]; then
    export BOARD
    export FLAVOR
fi

# Step 1: Build kernel to get source code (if not eBPF-only)
if [[ "$EBPF_ONLY" = false && "$DRY_RUN" = false ]]; then
    if [[ ! -d "build/linux-rockchip" ]]; then
        echo "Step 1: Building kernel to obtain source code..."
        log_verbose "Running: ./scripts/build-kernel.sh"
        ./scripts/build-kernel.sh
        echo "✓ Kernel source obtained"
        echo ""
    else
        echo "Step 1: Kernel source already exists"
        echo ""
    fi
fi

# Step 2: Configure eBPF options
if [[ "$SKIP_EBPF" = false ]]; then
    echo "Step 2: Configuring eBPF kernel options..."
    
    EBPF_ARGS="--verbose"
    if [[ "$DRY_RUN" = true ]]; then
        EBPF_ARGS="$EBPF_ARGS --dry-run"
    fi
    
    log_verbose "Running: ./scripts/enable-ebpf-kernel-config.sh $EBPF_ARGS"
    ./scripts/enable-ebpf-kernel-config.sh $EBPF_ARGS
    
    if [[ "$DRY_RUN" = false ]]; then
        echo "✓ eBPF configuration completed"
    else
        echo "✓ eBPF configuration analysis completed (dry-run)"
    fi
    echo ""
else
    echo "Step 2: eBPF configuration skipped"
    echo ""
fi

# Exit if eBPF-only mode
if [[ "$EBPF_ONLY" = true ]]; then
    echo "eBPF configuration completed. Use the following commands to continue:"
    echo ""
    echo "  # Rebuild kernel with eBPF support:"
    echo "  sudo SUITE=$SUITE ./scripts/build-kernel.sh"
    echo ""
    echo "  # Build complete image:"
    echo "  sudo SUITE=$SUITE BOARD=$BOARD FLAVOR=$FLAVOR ./build.sh"
    exit 0
fi

# Exit if dry-run mode
if [[ "$DRY_RUN" = true ]]; then
    echo "Dry-run completed. To apply changes and build, run:"
    echo "  sudo $0 -s $SUITE -b $BOARD -f $FLAVOR"
    exit 0
fi

# Step 3: Rebuild kernel with eBPF configuration
echo "Step 3: Rebuilding kernel with eBPF support..."
log_verbose "Running: ./scripts/build-kernel.sh"
./scripts/build-kernel.sh
echo "✓ Kernel rebuilt with eBPF support"
echo ""

# Step 4: Build complete image
echo "Step 4: Building complete system image..."
log_verbose "Running: ./build.sh"
./build.sh
echo "✓ System image built successfully"
echo ""

echo "Build completed successfully!"
echo ""
echo "Next steps:"
echo "1. Flash the generated image to your device"
echo "2. Boot the device with the new kernel"
echo "3. Verify eBPF support:"
echo "   - Check BTF: ls -la /sys/kernel/btf/vmlinux"
echo "   - Check config: zcat /proc/config.gz | grep CONFIG_BPF"
echo "   - Test eBPF: sudo bpftrace -e 'BEGIN { printf(\"eBPF works!\"); exit(); }'"
echo ""
echo "Generated files:"
find build -name "*.img" -o -name "*.deb" | head -5
echo "..."
