From 74dbb6b3d34da8b73ac3b4588a657f7adc02366e Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 11 Feb 2024 20:56:22 -0500
Subject: [PATCH 16/19] disable a few werrors for gcc13

---
 arch/arm/mach-rockchip/decode_bl31.py | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/arch/arm/mach-rockchip/decode_bl31.py b/arch/arm/mach-rockchip/decode_bl31.py
index 42fa32d23d2..1cff8c3a41a 100755
--- a/arch/arm/mach-rockchip/decode_bl31.py
+++ b/arch/arm/mach-rockchip/decode_bl31.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python2
+#!/usr/bin/env python3
 #
 # Copyright (C) 2020 Rockchip Electronics Co., Ltd
 #
-- 
2.25.1

