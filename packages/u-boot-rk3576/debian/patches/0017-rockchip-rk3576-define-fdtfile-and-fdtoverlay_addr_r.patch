From 3c662feed937f3dff7228c504914e5470a6270cb Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sat, 6 Jul 2024 12:18:20 -0400
Subject: [PATCH 17/19] rockchip: rk3576: define fdtfile and fdtoverlay_addr_r

---
 include/configs/rk3576_common.h   | 2 ++
 include/configs/rockchip-common.h | 6 ++++++
 2 files changed, 8 insertions(+)

diff --git a/include/configs/rk3576_common.h b/include/configs/rk3576_common.h
index 16abba314c5..26f08923146 100644
--- a/include/configs/rk3576_common.h
+++ b/include/configs/rk3576_common.h
@@ -67,6 +67,7 @@
 #define ENV_MEM_LAYOUT_SETTINGS \
 	"scriptaddr=0x40500000\0" \
 	"pxefile_addr_r=0x40600000\0" \
+	"fdtoverlay_addr_r=0x48200000\0" \
 	"fdt_addr_r=0x48300000\0" \
 	"kernel_addr_r=0x40400000\0" \
 	"kernel_addr_c=0x45480000\0" \
@@ -76,6 +77,7 @@
 
 #define CONFIG_EXTRA_ENV_SETTINGS \
 	ENV_MEM_LAYOUT_SETTINGS \
+	"fdtfile=" FDTFILE \
 	"partitions=" PARTS_RKIMG \
 	ROCKCHIP_DEVICE_SETTINGS \
 	RKIMG_DET_BOOTDEV \
diff --git a/include/configs/rockchip-common.h b/include/configs/rockchip-common.h
index 1d7b6e1c7c2..80cbff34edb 100644
--- a/include/configs/rockchip-common.h
+++ b/include/configs/rockchip-common.h
@@ -119,6 +119,12 @@
 	BOOT_TARGET_PXE(func) \
 	BOOT_TARGET_DHCP(func)
 
+#ifdef CONFIG_ARM64
+#define FDTFILE "rockchip/" CONFIG_DEFAULT_DEVICE_TREE ".dtb" "\0"
+#else
+#define FDTFILE CONFIG_DEFAULT_DEVICE_TREE ".dtb" "\0"
+#endif
+
 #ifdef CONFIG_ARM64
 #define ROOT_UUID "B921B045-1DF0-41C3-AF44-4C6F280D3FAE;\0"
 #else
-- 
2.25.1

