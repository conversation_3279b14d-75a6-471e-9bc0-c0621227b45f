From 0da1a9d5fee2bce6cf8d6c0bf0e08bbc20113649 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Mon, 8 Nov 2021 14:30:00 +0800
Subject: [PATCH 11/19] cmd: source: fix the error that the command source
 failed to execute

Signed-off-by: <PERSON> <<EMAIL>>
---
 cmd/source.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/cmd/source.c b/cmd/source.c
index 6b1c8b744b4..cf820c072af 100644
--- a/cmd/source.c
+++ b/cmd/source.c
@@ -87,7 +87,7 @@ source (ulong addr, const char *fit_uname)
 		 * past the zero-terminated sequence of image lengths to get
 		 * to the actual image data
 		 */
-		while (*data++ != IMAGE_PARAM_INVAL);
+		while (*data++);
 		break;
 #endif
 #if defined(CONFIG_FIT)
-- 
2.25.1

