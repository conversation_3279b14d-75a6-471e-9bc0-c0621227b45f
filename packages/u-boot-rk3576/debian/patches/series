0001-bootcmd-set-distro-boot-first.patch
0002-cmd-pxe-add-support-for-FDT-overlays.patch
0003-UPSTREAM-fs-ext4-Do-not-print-mount-fail-message-whe.patch
0004-UPSTREAM-fs-ext4-fix-crash-on-ext4ls.patch
0005-UPSTREAM-fs-ext4-skip-journal-state-if-fs-has-metada.patch
0006-UPSTREAM-distro_bootcmd-add-NVME-support.patch
0007-UPSTREAM-NVMe-do-PCI-enumerate-before-nvme-scan.patch
0008-rockchip-common-add-NVME-boot-for-distro-cmd.patch
0009-rockchip-Set-SD-to-have-a-higher-boot-priority-than-.patch
0010-rockchip-Set-USB-to-have-a-higher-boot-priority-than.patch
0011-cmd-source-fix-the-error-that-the-command-source-fai.patch
0012-rockchip-allow-passing-of-BL31-location-via-variable.patch
0013-rockchip-allow-passing-of-BL32-location-via-variable.patch
0014-arch-arm-mach-rockchip-fix-srctree-path.patch
0015-arch-arm-mach-rockchip-use-python3.patch
0016-disable-a-few-werrors-for-gcc13.patch
0017-rockchip-rk3576-define-fdtfile-and-fdtoverlay_addr_r.patch
0018-arm-dts-rk3576-define-spl-boot-order.patch
0019-add-dts-and-defconfig-of-armsom-sige5.patch

