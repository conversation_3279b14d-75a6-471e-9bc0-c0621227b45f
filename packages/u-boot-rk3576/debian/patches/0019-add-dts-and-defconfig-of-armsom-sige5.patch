From d1b0d58b0fd6c3f52cc56725adc2470c8b357b31 Mon Sep 17 00:00:00 2001
From: amazingfate <liu<PERSON><PERSON><EMAIL>>
Date: Tue, 7 May 2024 10:25:11 +0800
Subject: [PATCH 19/19] add dts and defconfig of armsom-sige5

---
 arch/arm/dts/rk3576-armsom-sige5.dts  |  14 ++
 configs/armsom-sige5-rk3576_defconfig | 222 ++++++++++++++++++++++++++
 2 files changed, 236 insertions(+)
 create mode 100644 arch/arm/dts/rk3576-armsom-sige5.dts
 create mode 100644 configs/armsom-sige5-rk3576_defconfig

diff --git a/arch/arm/dts/rk3576-armsom-sige5.dts b/arch/arm/dts/rk3576-armsom-sige5.dts
new file mode 100644
index 00000000000..8bccf050212
--- /dev/null
+++ b/arch/arm/dts/rk3576-armsom-sige5.dts
@@ -0,0 +1,14 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2023 Rockchip Electronics Co., Ltd.
+ */
+
+/dts-v1/;
+#include "rk3576.dtsi"
+#include "rk3576-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "ArmSoM Sige5";
+	compatible = "armsom,sige5", "rockchip,rk3576";
+};
diff --git a/configs/armsom-sige5-rk3576_defconfig b/configs/armsom-sige5-rk3576_defconfig
new file mode 100644
index 00000000000..8d6e4aa8f8b
--- /dev/null
+++ b/configs/armsom-sige5-rk3576_defconfig
@@ -0,0 +1,222 @@
+CONFIG_ARM=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_GPIO_SUPPORT=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3576=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_EVB_RK3576=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_ARMV8_CRYPTO=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3576-armsom-sige5"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_BOOTDELAY=0
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_BOARD_RNG_SEED=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_UFS_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_SPL_AB=y
+CONFIG_FASTBOOT_BUF_ADDR=0x40c00800
+CONFIG_FASTBOOT_BUF_SIZE=0x07000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPIO=y
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_UFS=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+CONFIG_OF_U_BOOT_REMOVE_PROPS="pinctrl-0 pinctrl-names clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_GPIO_HOG=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_I2C_MUX=y
+CONFIG_DM_KEY=y
+CONFIG_RK8XX_PWRKEY=y
+CONFIG_ADC_KEY=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_MODE=0x1
+CONFIG_SF_DEFAULT_SPEED=50000000
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_NANENG_COMBOPHY=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_USBDP=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_FUEL_GAUGE=y
+CONFIG_POWER_FG_CW201X=y
+CONFIG_POWER_FG_CW221X=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_RK8XX=y
+CONFIG_DM_POWER_DELIVERY=y
+CONFIG_TYPEC_TCPM=y
+CONFIG_TYPEC_TCPCI=y
+CONFIG_TYPEC_HUSB311=y
+CONFIG_TYPEC_FUSB302=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK8XX=y
+CONFIG_CHARGER_BQ25700=y
+CONFIG_CHARGER_BQ25890=y
+CONFIG_CHARGER_SGM41542=y
+CONFIG_DM_CHARGE_DISPLAY=y
+CONFIG_CHARGE_ANIMATION=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_SCSI=y
+CONFIG_DM_SCSI=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0x2ad40000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350e
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_UFS=y
+CONFIG_ROCKCHIP_UFS=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_ROCKCHIP_DW_HDMI_QP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_DW_DP=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_RGB=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX_HDMI=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_XBC=y
+CONFIG_LZ4=y
+CONFIG_SPL_GZIP=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
+CONFIG_OPTEE_CLIENT=y
+CONFIG_OPTEE_V2=y
-- 
2.25.1

