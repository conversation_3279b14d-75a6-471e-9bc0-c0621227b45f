Source: u-boot-rk3576
Section: admin
Priority: optional
Standards-Version: 4.6.0
Maintainer: <PERSON> <<EMAIL>>
Build-Depends: debhelper-compat (= 12), parted, gcc, make, bc, bison, python3, flex, device-tree-compiler, udev
Vcs-Git: https://github.com/radxa/u-boot.git

Package: u-boot-armsom-sige5
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-armsom-rk3576, u-boot
Replaces: u-boot-armsom-rk3576, u-boot
Conflicts: u-boot-armsom-rk3576, u-boot
Description: A boot loader for the ArmSoM Sige5.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
