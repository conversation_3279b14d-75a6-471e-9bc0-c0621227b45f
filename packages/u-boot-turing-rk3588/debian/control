Source: u-boot-turing-rk3588
Section: admin
Priority: optional
Maintainer: <PERSON> <<EMAIL>>
Build-Depends: 
 debhelper-compat (= 12),
 parted,
 gcc,
 make,
 bc,
 bison,
 flex,
 device-tree-compiler,
 udev,
 python3,
 libpython3-dev,
 python3-pyelftools,
 python3-setuptools,
 python3-distutils,
 python3-pkg-resources,
 swig,
 libfdt-dev,
 libssl-dev,
Standards-Version: 4.6.0
Homepage: https://www.denx.de/wiki/U-Boot/
Vcs-Browser: https://salsa.debian.org/debian/u-boot
Vcs-Git: https://salsa.debian.org/debian/u-boot.git

Package: u-boot-turing-rk1
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-turing-rk3588, u-boot
Replaces: u-boot-turing-rk3588, u-boot
Conflicts: u-boot-turing-rk3588, u-boot
Description: A boot loader for the Turing RK1.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-3b
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot
Replaces: u-boot
Conflicts: u-boot
Description: A boot loader for the Orangepi 3B.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-radxa-zero3
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot
Replaces: u-boot
Conflicts: u-boot
Description: A boot loader for the Radxa Zero 3.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
