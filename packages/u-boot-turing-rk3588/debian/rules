#!/usr/bin/make -f

include /usr/share/dpkg/architecture.mk
include /usr/share/dpkg/pkg-info.mk

ifneq ($(DEB_BUILD_GNU_TYPE),$(DEB_HOST_GNU_TYPE))
CROSS_COMPILE ?= $(DEB_HOST_GNU_TYPE)-
endif

ifneq (,$(filter parallel=%,$(DEB_BUILD_OPTIONS)))
NJOBS := -j $(patsubst parallel=%,%,$(filter parallel=%,$(DEB_BUILD_OPTIONS)))
else
NJOBS := -j $(shell nproc)
endif

include debian/targets.mk

${u-boot-rockchip_platforms}:
	@mkdir -p debian/build/$@

	make O=debian/build/$@ \
	  CROSS_COMPILE=$(CROSS_COMPILE) \
	  ARCH=arm \
	  $(NJOBS) \
	  $@_defconfig

	@touch .scmversion
	@sed -i 's/CONFIG_LOCALVERSION=""/CONFIG_LOCALVERSION=""/g' debian/build/$@/.config

	make O=debian/build/$@ \
	  CROSS_COMPILE=$(CROSS_COMPILE) \
	  SOURCE_DATE_EPOCH=$(shell date +%s) \
	  ARCH=arm \
	  $(NJOBS) \
	  BL31=../../../debian/rkbin/$($@_bl31) \
	  ROCKCHIP_TPL=../../../debian/rkbin/$($@_ddr)

build: ${u-boot-rockchip_platforms}

binary-arch: $(addprefix package-,${u-boot-rockchip_platforms})  

binary: binary-arch

package-%:
	@rm -rf debian/tmp
	@mkdir -m 755 -p debian/tmp/u-boot/usr/lib/u-boot/
	@mkdir -m 755 -p debian/tmp/u-boot/usr/bin/

	@cp debian/build/$(subst package-,,$@)/u-boot-rockchip.bin debian/tmp/u-boot/usr/lib/u-boot/u-boot-rockchip.bin
	@cp debian/build/$(subst package-,,$@)/u-boot-rockchip-spi.bin debian/tmp/u-boot/usr/lib/u-boot/u-boot-rockchip-spi.bin
	@cp debian/bin/u-boot-install debian/tmp/u-boot/usr/bin/u-boot-install
	@cp debian/bin/u-boot-install-mtd debian/tmp/u-boot/usr/bin/u-boot-install-mtd

	@mkdir -m 755 -p "debian/tmp/u-boot/DEBIAN"
	@cp debian/preinst debian/tmp/u-boot/DEBIAN/preinst
	@mkdir -p "debian/tmp/u-boot/usr/share/doc/u-boot"
	@cp debian/copyright "debian/tmp/u-boot/usr/share/doc/u-boot"
	@cp debian/changelog "debian/tmp/u-boot/usr/share/doc/u-boot/changelog.Debian"
	@gzip -9 "debian/tmp/u-boot/usr/share/doc/u-boot/changelog.Debian"
	@sh -c "cd 'debian/tmp/u-boot'; find . -type f ! -path './DEBIAN/*' -printf '%P\0' | xargs -r0 md5sum > DEBIAN/md5sums"
	@chown -R root:root "debian/tmp/u-boot" && chmod -R go-w "debian/tmp/u-boot" && chmod -R a+rX "debian/tmp/u-boot"
	@dpkg-gencontrol -pu-boot-$($(subst package-,,$@)_pkg) -P"debian/tmp/u-boot"
	@dpkg --build "debian/tmp/u-boot" ..

clean:
	@rm -rf debian/*tmp debian/tmp debian/build debian/files
	$(MAKE) clean
