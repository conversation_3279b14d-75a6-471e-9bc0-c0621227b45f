From 368b196e63d8b4e6eeee886e02e138b6872c6517 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 19 Jan 2024 16:22:50 -0500
Subject: [PATCH 2/3] arm: dts: rk3588-turing-rk1: fix pcie3x4 hang

---
 arch/arm/dts/rk3588-turing-rk1.dtsi | 6 +++++-
 1 file changed, 5 insertions(+), 1 deletion(-)

diff --git a/arch/arm/dts/rk3588-turing-rk1.dtsi b/arch/arm/dts/rk3588-turing-rk1.dtsi
index 9570b34aca..978b1a82da 100644
--- a/arch/arm/dts/rk3588-turing-rk1.dtsi
+++ b/arch/arm/dts/rk3588-turing-rk1.dtsi
@@ -226,7 +226,7 @@
 &pcie3x4 {
 	linux,pci-domain = <0>;
 	pinctrl-names = "default";
-	pinctrl-0 = <&pcie3_reset>;
+	pinctrl-0 = <&pcie3_reset>, <&pcie3_clkreqn_m1>;
 	reset-gpios = <&gpio4 RK_PB6 GPIO_ACTIVE_HIGH>;
 	vpcie3v3-supply = <&vcc3v3_pcie30>;
 	status = "okay";
@@ -259,6 +259,10 @@
 		vcc3v3_pcie30_en: pcie3-reg {
 			rockchip,pins = <2 RK_PC5 RK_FUNC_GPIO &pcfg_pull_none>;
 		};
+
+		pcie3_clkreqn_m1: pcie3-clkreqn-m1 {
+			rockchip,pins = <4 RK_PB4 RK_FUNC_GPIO &pcfg_pull_down>;
+		};
 	};
 
 	rtl8211f {
-- 
2.25.1

