From 8622cb14d7dc4604c073f842e89398d00ecb36c8 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 19 Jan 2024 16:30:57 -0500
Subject: [PATCH 3/3] arm64: dts: rockchip: Fix eMMC Data Strobe PD on rk3588

---
 arch/arm/dts/rk3588s-pinctrl.dtsi | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/arch/arm/dts/rk3588s-pinctrl.dtsi b/arch/arm/dts/rk3588s-pinctrl.dtsi
index 63151d9d23..30db12c4fc 100644
--- a/arch/arm/dts/rk3588s-pinctrl.dtsi
+++ b/arch/arm/dts/rk3588s-pinctrl.dtsi
@@ -369,7 +369,7 @@
 		emmc_data_strobe: emmc-data-strobe {
 			rockchip,pins =
 				/* emmc_data_strobe */
-				<2 RK_PA2 1 &pcfg_pull_none>;
+				<2 RK_PA2 1 &pcfg_pull_down>;
 		};
 	};
 
-- 
2.25.1

