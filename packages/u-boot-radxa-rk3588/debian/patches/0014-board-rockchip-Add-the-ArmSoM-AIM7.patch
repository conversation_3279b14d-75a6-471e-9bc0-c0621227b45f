From bcb031b538d53fbde497703db5e1689bd03a3fe2 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 17 Dec 2023 09:41:43 -0500
Subject: [PATCH 14/14] board: rockchip: Add the ArmSoM AIM7

---
 arch/arm/dts/rk3588-armsom-aim7.dts  |  60 +++++++
 configs/armsom-aim7-rk3588_defconfig | 233 +++++++++++++++++++++++++++
 2 files changed, 293 insertions(+)
 create mode 100644 arch/arm/dts/rk3588-armsom-aim7.dts
 create mode 100644 configs/armsom-aim7-rk3588_defconfig

diff --git a/arch/arm/dts/rk3588-armsom-aim7.dts b/arch/arm/dts/rk3588-armsom-aim7.dts
new file mode 100644
index 00000000000..385684153d3
--- /dev/null
+++ b/arch/arm/dts/rk3588-armsom-aim7.dts
@@ -0,0 +1,60 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2021 Rockchip Electronics Co., Ltd
+ *
+ */
+
+/dts-v1/;
+#include "rk3588.dtsi"
+#include "rk3588-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "ArmSoM AIM7";
+	compatible = "armsom,aim7", "rockchip,rk3588";
+
+	vcc12v_dcin: vcc12v-dcin {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc12v_dcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <12000000>;
+		regulator-max-microvolt = <12000000>;
+	};
+
+	vcc5v0_sys: vcc5v0-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_sys";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	adc-keys {
+		compatible = "adc-keys";
+		io-channels = <&saradc 1>;
+		io-channel-names = "buttons";
+		keyup-threshold-microvolt = <1800000>;
+		u-boot,dm-pre-reloc;
+		status = "okay";
+
+		volumeup-key {
+			u-boot,dm-pre-reloc;
+			linux,code = <KEY_VOLUMEUP>;
+			label = "volume up";
+			press-threshold-microvolt = <1750>;
+		};
+	};
+};
+
+&u2phy0 {
+	status = "okay";
+};
+
+&u2phy0_otg {
+	status = "okay";
+};
diff --git a/configs/armsom-aim7-rk3588_defconfig b/configs/armsom-aim7-rk3588_defconfig
new file mode 100644
index 00000000000..c1222c3dd00
--- /dev/null
+++ b/configs/armsom-aim7-rk3588_defconfig
@@ -0,0 +1,233 @@
+CONFIG_ARM=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_GPIO_SUPPORT=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3588=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_HWID_DTB=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_EVB_RK3588=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3588-armsom-aim7"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_BOOTDELAY=2
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MMC_WRITE=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_FASTBOOT_BUF_ADDR=0xc00800
+CONFIG_FASTBOOT_BUF_SIZE=0x04000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_SPL_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_SPL_SCMI_FIRMWARE=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_DM_KEY=y
+CONFIG_RK8XX_PWRKEY=y
+CONFIG_ADC_KEY=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_ROCKCHIP_OTP=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_NAND=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_SPEED=80000000
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SST=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_RGMII=y
+CONFIG_PHY_REALTEK=y
+CONFIG_NVME=y
+CONFIG_PCI=y
+CONFIG_DM_PCI=y
+CONFIG_DM_PCI_COMPAT=y
+CONFIG_PCIE_DW_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_NANENG_COMBOPHY=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_DM_ETH=y
+CONFIG_DM_ETH_PHY=y
+CONFIG_DWC_ETH_QOS=y
+CONFIG_GMAC_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_SNPS_PCIE3=y
+CONFIG_PHY_ROCKCHIP_USBDP=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_FUEL_GAUGE=y
+CONFIG_POWER_FG_CW201X=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_SPI_RK8XX=y
+CONFIG_DM_POWER_DELIVERY=y
+CONFIG_TYPEC_TCPM=y
+CONFIG_TYPEC_TCPCI=y
+CONFIG_TYPEC_HUSB311=y
+CONFIG_TYPEC_FUSB302=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK860X=y
+CONFIG_REGULATOR_RK806=y
+CONFIG_CHARGER_BQ25700=y
+CONFIG_CHARGER_BQ25890=y
+CONFIG_DM_CHARGE_DISPLAY=y
+CONFIG_CHARGE_ANIMATION=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_DM_RAMDISK=y
+CONFIG_RAMDISK_RO=y
+CONFIG_ROCKCHIP_SDRAM_COMMON=y
+CONFIG_ROCKCHIP_TPL_INIT_DRAM_TYPE=0
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0xFEB50000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SPI=y
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_PCI=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350a
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_ROCKCHIP_DW_HDMI_QP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_DW_DP=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX_HDMI=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_LZ4=y
+CONFIG_XBC=y
+CONFIG_LZMA=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
-- 
2.25.1

