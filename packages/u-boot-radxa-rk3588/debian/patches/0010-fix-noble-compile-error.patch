From a4416b8aea053d1b3d6cd466ceccc9661e296ac7 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 23 Feb 2024 13:54:20 -1000
Subject: [PATCH 10/14] fix noble compile error

---
 Makefile | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/Makefile b/Makefile
index 1012b065ad7..a44402263c9 100644
--- a/Makefile
+++ b/Makefile
@@ -396,6 +396,9 @@ export RCS_FIND_IGNORE := \( -name SCCS -o -name BitKeeper -o -name .svn -o    \
 			  -prune -o
 export RCS_TAR_IGNORE := --exclude SCCS --exclude Bit<PERSON>eeper --exclude .svn \
 			 --exclude CVS --exclude .pc --exclude .hg --exclude .git
+KBUILD_CFLAGS += $(call cc-option,-Wno-address)
+KBUILD_CFLAGS += $(call cc-option,-Wno-maybe-uninitialized)
+KBUILD_CFLAGS += $(call cc-option,-Wno-enum-int-mismatch)
 
 # ===========================================================================
 # Rules shared between *config targets and build targets
-- 
2.25.1

