From 79a3001f3ea10e63ddd3886cc446f293d866541c Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 23 Apr 2023 10:26:00 -0400
Subject: [PATCH 01/14] arch: arm: mach-rockchip: fix srctree path

---
 arch/arm/mach-rockchip/fit_nodes.sh    | 2 +-
 arch/arm/mach-rockchip/make_fit_atf.sh | 2 +-
 2 files changed, 2 insertions(+), 2 deletions(-)

diff --git a/arch/arm/mach-rockchip/fit_nodes.sh b/arch/arm/mach-rockchip/fit_nodes.sh
index 9639a06e1cc..dcf75092410 100755
--- a/arch/arm/mach-rockchip/fit_nodes.sh
+++ b/arch/arm/mach-rockchip/fit_nodes.sh
@@ -6,7 +6,7 @@
 #
 
 # Process args and auto set variables
-source ./${srctree}/arch/arm/mach-rockchip/fit_args.sh
+source ${srctree}/arch/arm/mach-rockchip/fit_args.sh
 rm -f ${srctree}/*.digest ${srctree}/*.bin.gz ${srctree}/bl31_0x*.bin
 
 # Periph register
diff --git a/arch/arm/mach-rockchip/make_fit_atf.sh b/arch/arm/mach-rockchip/make_fit_atf.sh
index 045273e3bd0..fa6a9780ed0 100755
--- a/arch/arm/mach-rockchip/make_fit_atf.sh
+++ b/arch/arm/mach-rockchip/make_fit_atf.sh
@@ -5,7 +5,7 @@
 # SPDX-License-Identifier:     GPL-2.0+
 #
 
-source ./${srctree}/arch/arm/mach-rockchip/fit_nodes.sh
+source ${srctree}/arch/arm/mach-rockchip/fit_nodes.sh
 
 gen_header
 gen_uboot_node
-- 
2.25.1

