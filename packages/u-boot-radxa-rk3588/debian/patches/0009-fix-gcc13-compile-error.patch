From 4e4bf7efb9af1630dcb2e01ccef555aef3764c8d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 11 Feb 2024 20:56:22 -0500
Subject: [PATCH 09/14] fix gcc13 compile error

---
 arch/arm/mach-rockchip/decode_bl31.py | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/arch/arm/mach-rockchip/decode_bl31.py b/arch/arm/mach-rockchip/decode_bl31.py
index 42fa32d23d2..1cff8c3a41a 100755
--- a/arch/arm/mach-rockchip/decode_bl31.py
+++ b/arch/arm/mach-rockchip/decode_bl31.py
@@ -1,4 +1,4 @@
-#!/usr/bin/env python2
+#!/usr/bin/env python3
 #
 # Copyright (C) 2020 Rockchip Electronics Co., Ltd
 #
-- 
2.25.1

