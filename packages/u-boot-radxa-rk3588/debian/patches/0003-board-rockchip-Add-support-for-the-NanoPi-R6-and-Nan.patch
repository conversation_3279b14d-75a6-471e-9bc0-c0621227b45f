From 9460a1533d54414b87580f88ee9e6a67d741e1fd Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 25 Jun 2023 14:45:56 -0400
Subject: [PATCH 03/14] board: rockchip: Add support for the NanoPi R6 and
 NanoPC T6

---
 arch/arm/dts/rk3588-nanopc-t6.dts    | 197 ++++++++++++++++++++++++
 arch/arm/dts/rk3588s-nanopi-r6c.dts  | 177 ++++++++++++++++++++++
 arch/arm/dts/rk3588s-nanopi-r6s.dts  | 125 +++++++++++++++
 configs/nanopc-t6-rk3588_defconfig   | 217 +++++++++++++++++++++++++++
 configs/nanopi-r6c-rk3588s_defconfig | 217 +++++++++++++++++++++++++++
 configs/nanopi-r6s-rk3588s_defconfig | 217 +++++++++++++++++++++++++++
 6 files changed, 1150 insertions(+)
 create mode 100644 arch/arm/dts/rk3588-nanopc-t6.dts
 create mode 100644 arch/arm/dts/rk3588s-nanopi-r6c.dts
 create mode 100644 arch/arm/dts/rk3588s-nanopi-r6s.dts
 create mode 100644 configs/nanopc-t6-rk3588_defconfig
 create mode 100644 configs/nanopi-r6c-rk3588s_defconfig
 create mode 100644 configs/nanopi-r6s-rk3588s_defconfig

diff --git a/arch/arm/dts/rk3588-nanopc-t6.dts b/arch/arm/dts/rk3588-nanopc-t6.dts
new file mode 100644
index 00000000000..648842ff849
--- /dev/null
+++ b/arch/arm/dts/rk3588-nanopc-t6.dts
@@ -0,0 +1,197 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2021 Rockchip Electronics Co., Ltd
+ *
+ */
+
+/dts-v1/;
+#include "rk3588.dtsi"
+#include "rk3588-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "NanoPC-T6";
+	compatible = "nanopc,nanopc-t6", "rockchip,rk3588";
+
+	vcc12v_dcin: vcc12v-dcin {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc12v_dcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <12000000>;
+		regulator-max-microvolt = <12000000>;
+	};
+
+	vcc5v0_sys: vcc5v0-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_sys";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc_5v0: vcc-5v0 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc_5v0";
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		regulator-boot-on;
+		regulator-always-on;
+		enable-active-high;
+		gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc_5v0_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc5v0_host: vcc5v0-host-regulator {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_host";
+		regulator-boot-on;
+		regulator-always-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		enable-active-high;
+		gpio = <&gpio4 RK_PB5 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc5v0_host_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc3v3_pcie30: vcc3v3-pcie30 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc3v3_pcie30";
+		regulator-min-microvolt = <3300000>;
+		regulator-max-microvolt = <3300000>;
+		enable-active-high;
+		gpio = <&gpio2 RK_PC5 GPIO_ACTIVE_HIGH>;
+		regulator-boot-on;
+		regulator-always-on;
+		startup-delay-us = <50000>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+       vdd_mpcie_3v3: vdd-mpcie-3v3 {
+               u-boot,dm-pre-reloc;
+               compatible = "regulator-fixed";
+               regulator-name = "vdd_mpcie_3v3";
+               pinctrl-names = "default";
+               pinctrl-0 = <&pcie_m2_1_pwren>;
+               regulator-min-microvolt = <3300000>;
+               regulator-max-microvolt = <3300000>;
+               enable-active-high;
+               gpio = <&gpio4 RK_PC2 GPIO_ACTIVE_HIGH>;
+               regulator-boot-on;
+               regulator-always-on;
+               vin-supply = <&vcc5v0_sys>;
+       };
+
+	led_sys: led-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "led_sys";
+		enable-active-high;
+		gpio = <&gpio1 RK_PC1 GPIO_ACTIVE_HIGH>; // Turn on user led
+		regulator-boot-on;
+		regulator-always-on;
+		vin-supply = <&vcc5v0_sys>;
+	};
+};
+
+&pcie3x4 {
+	u-boot,dm-pre-reloc;
+	vpcie3v3-supply = <&vcc3v3_pcie30>;
+	reset-gpios = <&gpio4 RK_PB6 GPIO_ACTIVE_HIGH>;
+	status = "okay";
+};
+
+&pcie30phy {
+	u-boot,dm-pre-reloc;
+	status = "okay";
+};
+
+&pcie2x1l1 {
+       u-boot,dm-pre-reloc;
+       vpcie3v3-supply = <&vdd_mpcie_3v3>;
+       reset-gpios = <&gpio4 RK_PA2 GPIO_ACTIVE_HIGH>;
+       status = "okay";
+};
+
+&combphy0_ps {
+	u-boot,dm-pre-reloc;
+	status = "okay";
+};
+
+&combphy1_ps {
+       u-boot,dm-pre-reloc;
+       status = "okay";
+};
+
+&combphy2_psu {
+	u-boot,dm-pre-reloc;
+	status = "okay";
+};
+
+&usb2phy0_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0_otg {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb2phy2_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2_host {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&pinctrl {
+	usb {
+		u-boot,dm-pre-reloc;
+		vcc5v0_host_en: vcc5v0-host-en {
+			u-boot,dm-pre-reloc;
+			rockchip,pins = <4 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+	power {
+		u-boot,dm-spl;
+		vcc_5v0_en: vcc-5v0-en {
+			u-boot,dm-spl;
+			rockchip,pins = <4 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+       pcie {
+               u-boot,dm-pre-reloc;
+               pcie_m2_1_pwren: pcie-m21-pwren {
+                       u-boot,dm-pre-reloc;
+                       rockchip,pins = <4 RK_PC2 RK_FUNC_GPIO &pcfg_pull_none>;
+               };
+       };
+
+};
diff --git a/arch/arm/dts/rk3588s-nanopi-r6c.dts b/arch/arm/dts/rk3588s-nanopi-r6c.dts
new file mode 100644
index 00000000000..3c55e0d89a2
--- /dev/null
+++ b/arch/arm/dts/rk3588s-nanopi-r6c.dts
@@ -0,0 +1,177 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2021 Rockchip Electronics Co., Ltd
+ *
+ */
+
+/dts-v1/;
+#include "rk3588.dtsi"
+#include "rk3588-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "NanoPi R6C";
+	compatible = "nanopi,nanopi-r6c", "rockchip,rk3588";
+
+	vcc12v_dcin: vcc12v-dcin {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc12v_dcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <12000000>;
+		regulator-max-microvolt = <12000000>;
+	};
+
+	vcc5v0_sys: vcc5v0-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_sys";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc_5v0: vcc-5v0 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc_5v0";
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		regulator-boot-on;
+		regulator-always-on;
+		enable-active-high;
+		gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc_5v0_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc5v0_host: vcc5v0-host-regulator {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_host";
+		regulator-boot-on;
+		regulator-always-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		enable-active-high;
+		gpio = <&gpio4 RK_PB5 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc5v0_host_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc3v3_pcie2x1l2: vcc3v3-pcie2x1l2 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc3v3_pcie2x1l2";
+		regulator-min-microvolt = <1800000>;
+		regulator-max-microvolt = <1800000>;
+		enable-active-high;
+		//regulator-boot-on;
+		//regulator-always-on;
+		//gpios = <&gpio0 RK_PC5 GPIO_ACTIVE_HIGH>;
+		startup-delay-us = <50000>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc3v3_pcie2x1l1: vcc3v3-pcie2x1l2 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc3v3_pcie2x1l2";
+		regulator-min-microvolt = <1800000>;
+		regulator-max-microvolt = <1800000>;
+		enable-active-high;
+		//regulator-boot-on;
+		//regulator-always-on;
+		//gpios = <&gpio0 RK_PC5 GPIO_ACTIVE_HIGH>;
+		startup-delay-us = <50000>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	led_sys: led-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "led_sys";
+		enable-active-high;
+		gpio = <&gpio1 RK_PC1 GPIO_ACTIVE_HIGH>; // Turn on user led
+		regulator-boot-on;
+		regulator-always-on;
+		vin-supply = <&vcc5v0_sys>;
+	};
+};
+
+&pcie2x1l1 {
+	reset-gpios = <&gpio1 RK_PA7 GPIO_ACTIVE_HIGH>;
+	rockchip,init-delay-ms = <100>;
+	vpcie3v3-supply = <&vcc3v3_pcie2x1l1>;
+	status = "okay";
+};
+
+&pcie2x1l2 {
+	u-boot,dm-pre-reloc;
+	reset-gpios = <&gpio3 RK_PD1 GPIO_ACTIVE_HIGH>;
+	vpcie3v3-supply = <&vcc3v3_pcie2x1l2>;
+	status = "okay";
+};
+
+&combphy0_ps {
+	u-boot,dm-pre-reloc;
+	status = "okay";
+};
+
+&combphy2_psu {
+	u-boot,dm-pre-reloc;
+	status = "okay";
+};
+
+&usb2phy0_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0_otg {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb2phy2_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2_host {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&pinctrl {
+	usb {
+		u-boot,dm-pre-reloc;
+		vcc5v0_host_en: vcc5v0-host-en {
+			u-boot,dm-pre-reloc;
+			rockchip,pins = <4 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+	power {
+		u-boot,dm-spl;
+		vcc_5v0_en: vcc-5v0-en {
+			u-boot,dm-spl;
+			rockchip,pins = <4 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+};
diff --git a/arch/arm/dts/rk3588s-nanopi-r6s.dts b/arch/arm/dts/rk3588s-nanopi-r6s.dts
new file mode 100644
index 00000000000..ac300cd42fc
--- /dev/null
+++ b/arch/arm/dts/rk3588s-nanopi-r6s.dts
@@ -0,0 +1,125 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2021 Rockchip Electronics Co., Ltd
+ *
+ */
+
+/dts-v1/;
+#include "rk3588.dtsi"
+#include "rk3588-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "NanoPi R6S";
+	compatible = "nanopi,nanopi-r6s", "rockchip,rk3588";
+
+	vcc12v_dcin: vcc12v-dcin {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc12v_dcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <12000000>;
+		regulator-max-microvolt = <12000000>;
+	};
+
+	vcc5v0_sys: vcc5v0-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_sys";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc_5v0: vcc-5v0 {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc_5v0";
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		regulator-boot-on;
+		regulator-always-on;
+		enable-active-high;
+		gpio = <&gpio4 RK_PA3 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc_5v0_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc5v0_host: vcc5v0-host-regulator {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_host";
+		regulator-boot-on;
+		regulator-always-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		enable-active-high;
+		gpio = <&gpio4 RK_PB5 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc5v0_host_en>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	led_sys: led-sys {
+		u-boot,dm-pre-reloc;
+		compatible = "regulator-fixed";
+		regulator-name = "led_sys";
+		enable-active-high;
+		gpio = <&gpio1 RK_PC1 GPIO_ACTIVE_HIGH>; // Turn on user led
+		regulator-boot-on;
+		regulator-always-on;
+		vin-supply = <&vcc5v0_sys>;
+	};
+};
+
+&usb2phy0_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy0_otg {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb2phy2_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2_host {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&pinctrl {
+	usb {
+		u-boot,dm-pre-reloc;
+		vcc5v0_host_en: vcc5v0-host-en {
+			u-boot,dm-pre-reloc;
+			rockchip,pins = <4 RK_PB5 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+	power {
+		u-boot,dm-spl;
+		vcc_5v0_en: vcc-5v0-en {
+			u-boot,dm-spl;
+			rockchip,pins = <4 RK_PA3 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+};
diff --git a/configs/nanopc-t6-rk3588_defconfig b/configs/nanopc-t6-rk3588_defconfig
new file mode 100644
index 00000000000..1d216cb1dda
--- /dev/null
+++ b/configs/nanopc-t6-rk3588_defconfig
@@ -0,0 +1,217 @@
+CONFIG_ARM=y
+CONFIG_ARM_CPU_SUSPEND=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_GPIO_SUPPORT=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3588=y
+CONFIG_ROCKCHIP_BOOTDEV="mmc 0"
+CONFIG_ROCKCHIP_USB_BOOT=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_HWID_DTB=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_ROCKCHIP_EMMC_IOMUX=y
+CONFIG_LOADER_INI="RK3588MINIALL.ini"
+CONFIG_TRUST_INI="RK3588TRUST.ini"
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_EVB_RK3588=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3588-nanopc-t6"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_BOOTDELAY=2
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MMC_WRITE=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_FASTBOOT_BUF_ADDR=0xc00800
+CONFIG_FASTBOOT_BUF_SIZE=0x04000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_SPL_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_SPL_SCMI_FIRMWARE=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_DM_KEY=y
+CONFIG_ADC_KEY=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_OTP=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_NAND=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_SPEED=80000000
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SST=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_DM_ETH=y
+CONFIG_DM_ETH_PHY=y
+CONFIG_DWC_ETH_QOS=y
+CONFIG_GMAC_ROCKCHIP=y
+CONFIG_NVME=y
+CONFIG_PCI=y
+CONFIG_DM_PCI=y
+CONFIG_DM_PCI_COMPAT=y
+CONFIG_PCIE_DW_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_SNPS_PCIE3=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_SPI_RK8XX=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK860X=y
+CONFIG_REGULATOR_RK806=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_ROCKCHIP_SDRAM_COMMON=y
+CONFIG_ROCKCHIP_TPL_INIT_DRAM_TYPE=0
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0xFEB50000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SPI=y
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_XHCI_PCI=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350a
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_LZ4=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
+CONFIG_PHY_ROCKCHIP_NANENG_COMBOPHY=y
diff --git a/configs/nanopi-r6c-rk3588s_defconfig b/configs/nanopi-r6c-rk3588s_defconfig
new file mode 100644
index 00000000000..a9b5fa79bf9
--- /dev/null
+++ b/configs/nanopi-r6c-rk3588s_defconfig
@@ -0,0 +1,217 @@
+CONFIG_ARM=y
+CONFIG_ARM_CPU_SUSPEND=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_GPIO_SUPPORT=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3588=y
+CONFIG_ROCKCHIP_BOOTDEV="mmc 0"
+CONFIG_ROCKCHIP_USB_BOOT=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_HWID_DTB=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_ROCKCHIP_EMMC_IOMUX=y
+CONFIG_LOADER_INI="RK3588MINIALL.ini"
+CONFIG_TRUST_INI="RK3588TRUST.ini"
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_EVB_RK3588=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3588s-nanopi-r6c"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_BOOTDELAY=2
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MMC_WRITE=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_FASTBOOT_BUF_ADDR=0xc00800
+CONFIG_FASTBOOT_BUF_SIZE=0x04000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_SPL_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_SPL_SCMI_FIRMWARE=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_DM_KEY=y
+CONFIG_ADC_KEY=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_OTP=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_NAND=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_SPEED=80000000
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SST=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_DM_ETH=y
+CONFIG_DM_ETH_PHY=y
+CONFIG_DWC_ETH_QOS=y
+CONFIG_GMAC_ROCKCHIP=y
+CONFIG_NVME=y
+CONFIG_PCI=y
+CONFIG_DM_PCI=y
+CONFIG_DM_PCI_COMPAT=y
+CONFIG_PCIE_DW_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_SNPS_PCIE3=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_SPI_RK8XX=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK860X=y
+CONFIG_REGULATOR_RK806=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_ROCKCHIP_SDRAM_COMMON=y
+CONFIG_ROCKCHIP_TPL_INIT_DRAM_TYPE=0
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0xFEB50000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SPI=y
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_XHCI_PCI=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350a
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_LZ4=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
+CONFIG_PHY_ROCKCHIP_NANENG_COMBOPHY=y
\ No newline at end of file
diff --git a/configs/nanopi-r6s-rk3588s_defconfig b/configs/nanopi-r6s-rk3588s_defconfig
new file mode 100644
index 00000000000..6c5c891e7fd
--- /dev/null
+++ b/configs/nanopi-r6s-rk3588s_defconfig
@@ -0,0 +1,217 @@
+CONFIG_ARM=y
+CONFIG_ARM_CPU_SUSPEND=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_GPIO_SUPPORT=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3588=y
+CONFIG_ROCKCHIP_BOOTDEV="mmc 0"
+CONFIG_ROCKCHIP_USB_BOOT=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_HWID_DTB=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_ROCKCHIP_EMMC_IOMUX=y
+CONFIG_LOADER_INI="RK3588MINIALL.ini"
+CONFIG_TRUST_INI="RK3588TRUST.ini"
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_EVB_RK3588=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3588s-nanopi-r6s"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_BOOTDELAY=2
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MMC_WRITE=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_FASTBOOT_BUF_ADDR=0xc00800
+CONFIG_FASTBOOT_BUF_SIZE=0x04000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_PCI=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_SPL_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_SPL_SCMI_FIRMWARE=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_DM_KEY=y
+CONFIG_ADC_KEY=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_OTP=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_NAND=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_SPEED=80000000
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SST=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_DM_ETH=y
+CONFIG_DM_ETH_PHY=y
+CONFIG_DWC_ETH_QOS=y
+CONFIG_GMAC_ROCKCHIP=y
+CONFIG_NVME=y
+CONFIG_PCI=y
+CONFIG_DM_PCI=y
+CONFIG_DM_PCI_COMPAT=y
+CONFIG_PCIE_DW_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_SNPS_PCIE3=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_SPI_RK8XX=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK860X=y
+CONFIG_REGULATOR_RK806=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_ROCKCHIP_SDRAM_COMMON=y
+CONFIG_ROCKCHIP_TPL_INIT_DRAM_TYPE=0
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0xFEB50000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SPI=y
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_XHCI_PCI=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350a
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_LZ4=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
+CONFIG_PHY_ROCKCHIP_NANENG_COMBOPHY=y
-- 
2.25.1

