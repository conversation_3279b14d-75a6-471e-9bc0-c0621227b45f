From e688f798b8f3e5682a8ce58325cc5d3514334d8b Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Sat, 20 Jul 2024 16:11:38 +0000
Subject: [PATCH 13/14] add support for aio-3588l

---
 arch/arm/dts/rk3588-aio-3588l.dts  | 261 +++++++++++++++++++++++++++++
 configs/aio-3588l-rk3588_defconfig | 236 ++++++++++++++++++++++++++
 2 files changed, 497 insertions(+)
 create mode 100755 arch/arm/dts/rk3588-aio-3588l.dts
 create mode 100644 configs/aio-3588l-rk3588_defconfig

diff --git a/arch/arm/dts/rk3588-aio-3588l.dts b/arch/arm/dts/rk3588-aio-3588l.dts
new file mode 100755
index 00000000000..e0c66434e93
--- /dev/null
+++ b/arch/arm/dts/rk3588-aio-3588l.dts
@@ -0,0 +1,261 @@
+// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
+/*
+ * Copyright (c) 2021 Rockchip Electronics Co., Ltd
+ *
+ */
+
+/dts-v1/;
+#include "rk3588.dtsi"
+#include "rk3588-u-boot.dtsi"
+#include <dt-bindings/input/input.h>
+
+/ {
+	model = "ROC-RK3588S-PC";
+	compatible = "firefly,roc-rk3588s-pc", "firefly,station-m3", "rockchip,rk3588";
+
+	config {
+		u-boot,dm-pre-reloc;
+		u-boot,boot-led = "red";
+	};
+
+	vcc12v_dcin: vcc12v-dcin {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc12v_dcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <12000000>;
+		regulator-max-microvolt = <12000000>;
+	};
+
+	vcc3v3_pcie20: vcc3v3-pcie20 {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc3v3_pcie20";
+		regulator-min-microvolt = <3300000>;
+		regulator-max-microvolt = <3300000>;
+		regulator-always-on;
+		enable-active-high;
+		gpios = <&gpio1 RK_PD7 GPIO_ACTIVE_HIGH>;
+		startup-delay-us = <5000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc5v0_host: vcc5v0-host {
+		status = "okay";
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_host";
+		regulator-boot-on;
+		regulator-always-on;
+		reset-delay-us = <200000>;
+		startup-delay-us = <1200000>;
+		enable-active-high;
+		gpio = <&gpio1 RK_PB6 GPIO_ACTIVE_HIGH>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&vcc5v0_host_en>;
+		vin-supply = <&vcc5v0_usb>;
+	};
+
+	vcc5v0_sys: vcc5v0-sys {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_sys";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vbus5v0_typec_pwr_en: vbus5v0-typec-pwr-en-regulator {
+		status = "okay";
+		compatible = "regulator-fixed";
+		enable-active-high;
+		regulator-name = "vbus5v0_typec_pwr_en";
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		gpio = <&gpio1 RK_PB1 GPIO_ACTIVE_HIGH>;
+		vin-supply = <&vcc5v0_usb>;
+		pinctrl-names = "default";
+		pinctrl-0 = <&typec5v_pwren>;
+	};
+
+	vcc5v0_usbdcin: vcc5v0-usbdcin {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_usbdcin";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc5v0_usb: vcc5v0-usb {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc5v0_usb";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <5000000>;
+		regulator-max-microvolt = <5000000>;
+		vin-supply = <&vcc12v_dcin>;
+	};
+
+	vcc_1v1_nldo_s3: vcc-1v1-nldo-s3 {
+		compatible = "regulator-fixed";
+		regulator-name = "vcc_1v1_nldo_s3";
+		regulator-always-on;
+		regulator-boot-on;
+		regulator-min-microvolt = <1100000>;
+		regulator-max-microvolt = <1100000>;
+		vin-supply = <&vcc5v0_sys>;
+	};
+
+	vcc_hub_reset: vcc-hub-reset-regulator {
+		status = "okay";
+		compatible = "regulator-fixed"; 
+		regulator-name = "vcc_hub_reset";
+		regulator-boot-on;
+		regulator-always-on; 
+		enable-active-high;
+		gpio = <&gpio1 RK_PB0 GPIO_ACTIVE_HIGH>;
+	};
+
+	firefly_leds: leds {
+		status = "okay";
+		u-boot,dm-pre-reloc;
+		compatible = "gpio-leds";
+		pinctrl-names = "default";
+		pinctrl-0 =<&leds_gpio>;
+		blue_led: blue-led {
+			u-boot,dm-pre-reloc;
+			label = "blue";
+			default-state = "on";
+			gpios = <&gpio1 RK_PD5 GPIO_ACTIVE_HIGH>;//blue led
+		};
+
+		red_led: red-led {
+			u-boot,dm-pre-reloc;
+			label = "red";
+			default-state = "off";
+			gpios = <&gpio3 RK_PB2 GPIO_ACTIVE_HIGH>;//red led
+		};
+
+		green_led: green-led {
+			u-boot,dm-pre-reloc;
+			label = "green"; 
+			default-state = "off";
+			gpios = <&gpio3 RK_PC0 GPIO_ACTIVE_HIGH>;//green led
+		};
+	};
+
+	adc_keys: adc-keys {
+		status = "okay";
+		u-boot,dm-pre-reloc;
+		compatible = "adc-keys";
+		io-channels = <&saradc 1>;
+		io-channel-names = "buttons";
+		keyup-threshold-microvolt = <1800000>;
+		poll-interval = <100>;
+
+		recovery-key{
+			u-boot,dm-pre-reloc;
+			label = "F12";
+			linux,code = <KEY_F12>;
+			press-threshold-microvolt = <17000>;
+		};
+	};
+};
+
+&combphy0_ps {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&pcie2x1l2 {
+	status = "okay";
+	reset-gpios = <&gpio3 RK_PD1 GPIO_ACTIVE_HIGH>;
+	u-boot,dm-pre-reloc;
+	vpcie3v3-supply = <&vcc3v3_pcie20>;
+};
+
+&usb_host0_ehci {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb_host0_ohci {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb2phy2_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy2_host {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb_host1_ehci {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb_host1_ohci {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&usb2phy3_grf {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy3 {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&u2phy3_host {
+	status = "okay";
+	u-boot,dm-pre-reloc;
+};
+
+&gmac0 {
+	status = "disabled";
+	u-boot,dm-pre-reloc;
+};
+
+&pinctrl {
+	leds {
+		leds_gpio: leds-gpio {
+			rockchip,pins =
+				/* led_user */
+				<3 RK_PB2 RK_FUNC_GPIO &pcfg_pull_none>,
+				/* led_power */
+				<1 RK_PD5 RK_FUNC_GPIO &pcfg_pull_none>,
+				/* led_user1 */
+				<3 RK_PC0 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+	usb {
+		vcc5v0_host_en: vcc5v0-host-en {
+			rockchip,pins = <1 RK_PB6 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+
+	usb-typec {
+		usbc0_int: usbc0-int {
+			rockchip,pins = <0 RK_PC4 RK_FUNC_GPIO &pcfg_pull_up>;
+		};
+
+		typec5v_pwren: typec5v-pwren {
+			rockchip,pins = <1 RK_PB1 RK_FUNC_GPIO &pcfg_pull_none>;
+		};
+	};
+};
diff --git a/configs/aio-3588l-rk3588_defconfig b/configs/aio-3588l-rk3588_defconfig
new file mode 100644
index 00000000000..3eaa7518db7
--- /dev/null
+++ b/configs/aio-3588l-rk3588_defconfig
@@ -0,0 +1,236 @@
+CONFIG_ARM=y
+CONFIG_ARCH_ROCKCHIP=y
+CONFIG_SPL_LIBCOMMON_SUPPORT=y
+CONFIG_SPL_LIBGENERIC_SUPPORT=y
+CONFIG_SYS_MALLOC_F_LEN=0x80000
+CONFIG_SPL_FIT_GENERATOR="arch/arm/mach-rockchip/make_fit_atf.sh"
+CONFIG_ROCKCHIP_RK3588=y
+CONFIG_ROCKCHIP_FIT_IMAGE=y
+CONFIG_ROCKCHIP_HWID_DTB=y
+CONFIG_ROCKCHIP_VENDOR_PARTITION=y
+CONFIG_USING_KERNEL_DTB_V2=y
+CONFIG_ROCKCHIP_FIT_IMAGE_PACK=y
+CONFIG_ROCKCHIP_NEW_IDB=y
+CONFIG_PSTORE=y
+CONFIG_SPL_SERIAL_SUPPORT=y
+CONFIG_SPL_DRIVERS_MISC_SUPPORT=y
+CONFIG_TARGET_ROC_RK3588S_PC=y
+CONFIG_SPL_LIBDISK_SUPPORT=y
+CONFIG_SPL_SPI_FLASH_SUPPORT=y
+CONFIG_SPL_SPI_SUPPORT=y
+CONFIG_DEFAULT_DEVICE_TREE="rk3588-aio-3588l"
+CONFIG_DEBUG_UART=y
+CONFIG_FIT=y
+CONFIG_FIT_IMAGE_POST_PROCESS=y
+CONFIG_FIT_HW_CRYPTO=y
+CONFIG_SPL_LOAD_FIT=y
+CONFIG_SPL_FIT_IMAGE_POST_PROCESS=y
+CONFIG_SPL_FIT_HW_CRYPTO=y
+# CONFIG_SPL_SYS_DCACHE_OFF is not set
+CONFIG_SPL_FIT_IMAGE_KB=4096
+CONFIG_BOOTDELAY=5
+CONFIG_SYS_CONSOLE_INFO_QUIET=y
+# CONFIG_DISPLAY_CPUINFO is not set
+CONFIG_ANDROID_BOOTLOADER=y
+CONFIG_ANDROID_AVB=y
+CONFIG_ANDROID_BOOT_IMAGE_HASH=y
+CONFIG_SPL_BOARD_INIT=y
+# CONFIG_SPL_RAW_IMAGE_SUPPORT is not set
+# CONFIG_SPL_LEGACY_IMAGE_SUPPORT is not set
+CONFIG_SPL_SEPARATE_BSS=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_USE_PARTITION=y
+CONFIG_SYS_MMCSD_RAW_MODE_U_BOOT_PARTITION=0x1
+CONFIG_SPL_MMC_WRITE=y
+CONFIG_SPL_MTD_SUPPORT=y
+CONFIG_SPL_ATF=y
+CONFIG_SPL_AB=y
+CONFIG_FASTBOOT_BUF_ADDR=0xc00800
+CONFIG_FASTBOOT_BUF_SIZE=0x07000000
+CONFIG_FASTBOOT_FLASH=y
+CONFIG_FASTBOOT_FLASH_MMC_DEV=0
+CONFIG_CMD_BOOTZ=y
+CONFIG_CMD_DTIMG=y
+# CONFIG_CMD_ELF is not set
+# CONFIG_CMD_IMI is not set
+# CONFIG_CMD_IMLS is not set
+# CONFIG_CMD_XIMG is not set
+# CONFIG_CMD_LZMADEC is not set
+# CONFIG_CMD_UNZIP is not set
+# CONFIG_CMD_FLASH is not set
+# CONFIG_CMD_FPGA is not set
+CONFIG_CMD_GPT=y
+# CONFIG_CMD_LOADB is not set
+# CONFIG_CMD_LOADS is not set
+CONFIG_CMD_BOOT_ANDROID=y
+CONFIG_CMD_MMC=y
+CONFIG_CMD_SF=y
+CONFIG_CMD_SPI=y
+CONFIG_CMD_USB=y
+CONFIG_CMD_USB_MASS_STORAGE=y
+# CONFIG_CMD_ITEST is not set
+# CONFIG_CMD_SETEXPR is not set
+CONFIG_CMD_TFTPPUT=y
+CONFIG_CMD_TFTP_BOOTM=y
+CONFIG_CMD_TFTP_FLASH=y
+# CONFIG_CMD_MISC is not set
+CONFIG_CMD_MTD_BLK=y
+# CONFIG_SPL_DOS_PARTITION is not set
+# CONFIG_ISO_PARTITION is not set
+CONFIG_EFI_PARTITION_ENTRIES_NUMBERS=64
+CONFIG_SPL_OF_CONTROL=y
+CONFIG_SPL_DTB_MINIMUM=y
+CONFIG_OF_LIVE=y
+CONFIG_OF_SPL_REMOVE_PROPS="clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+CONFIG_OF_U_BOOT_REMOVE_PROPS="pinctrl-0 pinctrl-names clock-names interrupt-parent assigned-clocks assigned-clock-rates assigned-clock-parents"
+# CONFIG_NET_TFTP_VARS is not set
+CONFIG_REGMAP=y
+CONFIG_SPL_REGMAP=y
+CONFIG_SYSCON=y
+CONFIG_SPL_SYSCON=y
+# CONFIG_SARADC_ROCKCHIP is not set
+CONFIG_SARADC_ROCKCHIP_V2=y
+CONFIG_CLK=y
+CONFIG_SPL_CLK=y
+CONFIG_CLK_SCMI=y
+CONFIG_SPL_CLK_SCMI=y
+CONFIG_DM_CRYPTO=y
+CONFIG_SPL_DM_CRYPTO=y
+CONFIG_ROCKCHIP_CRYPTO_V2=y
+CONFIG_SPL_ROCKCHIP_CRYPTO_V2=y
+CONFIG_DM_RNG=y
+CONFIG_RNG_ROCKCHIP=y
+CONFIG_SCMI_FIRMWARE=y
+CONFIG_SPL_SCMI_FIRMWARE=y
+CONFIG_GPIO_HOG=y
+CONFIG_ROCKCHIP_GPIO=y
+CONFIG_ROCKCHIP_GPIO_V2=y
+CONFIG_DM_PCA953X=y
+CONFIG_SYS_I2C_ROCKCHIP=y
+CONFIG_I2C_MUX=y
+CONFIG_DM_KEY=y
+CONFIG_RK8XX_PWRKEY=y
+CONFIG_ADC_KEY=y
+CONFIG_LED=y
+CONFIG_LED_GPIO=y
+CONFIG_MISC=y
+CONFIG_SPL_MISC=y
+CONFIG_MISC_DECOMPRESS=y
+CONFIG_SPL_MISC_DECOMPRESS=y
+CONFIG_ROCKCHIP_OTP=y
+CONFIG_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_HW_DECOMPRESS=y
+CONFIG_SPL_ROCKCHIP_SECURE_OTP=y
+CONFIG_MMC_DW=y
+CONFIG_MMC_DW_ROCKCHIP=y
+CONFIG_MMC_SDHCI=y
+CONFIG_MMC_SDHCI_SDMA=y
+CONFIG_MMC_SDHCI_ROCKCHIP=y
+CONFIG_MTD=y
+CONFIG_MTD_BLK=y
+CONFIG_MTD_DEVICE=y
+CONFIG_NAND=y
+CONFIG_MTD_SPI_NAND=y
+CONFIG_SPI_FLASH=y
+CONFIG_SF_DEFAULT_SPEED=80000000
+CONFIG_SPI_FLASH_EON=y
+CONFIG_SPI_FLASH_GIGADEVICE=y
+CONFIG_SPI_FLASH_MACRONIX=y
+CONFIG_SPI_FLASH_SST=y
+CONFIG_SPI_FLASH_WINBOND=y
+CONFIG_SPI_FLASH_XMC=y
+CONFIG_SPI_FLASH_XTX=y
+CONFIG_SPI_FLASH_MTD=y
+CONFIG_DM_ETH=y
+CONFIG_DM_ETH_PHY=y
+CONFIG_DWC_ETH_QOS=y
+CONFIG_GMAC_ROCKCHIP=y
+CONFIG_PHY_ROCKCHIP_INNO_USB2=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX=y
+CONFIG_PHY_ROCKCHIP_USBDP=y
+CONFIG_PINCTRL=y
+CONFIG_SPL_PINCTRL=y
+CONFIG_DM_FUEL_GAUGE=y
+CONFIG_POWER_FG_CW201X=y
+CONFIG_POWER_FG_CW221X=y
+CONFIG_DM_PMIC=y
+CONFIG_PMIC_SPI_RK8XX=y
+CONFIG_DM_POWER_DELIVERY=y
+CONFIG_TYPEC_TCPM=y
+CONFIG_TYPEC_TCPCI=y
+CONFIG_TYPEC_HUSB311=y
+CONFIG_TYPEC_FUSB302=y
+CONFIG_REGULATOR_PWM=y
+CONFIG_DM_REGULATOR_FIXED=y
+CONFIG_DM_REGULATOR_GPIO=y
+CONFIG_REGULATOR_RK860X=y
+CONFIG_REGULATOR_RK806=y
+CONFIG_CHARGER_BQ25700=y
+CONFIG_CHARGER_BQ25890=y
+CONFIG_CHARGER_SC8551=y
+CONFIG_CHARGER_SGM41542=y
+CONFIG_DM_CHARGE_DISPLAY=y
+CONFIG_CHARGE_ANIMATION=y
+CONFIG_PWM_ROCKCHIP=y
+CONFIG_RAM=y
+CONFIG_SPL_RAM=y
+CONFIG_TPL_RAM=y
+CONFIG_DM_RAMDISK=y
+CONFIG_RAMDISK_RO=y
+CONFIG_DM_RESET=y
+CONFIG_SPL_DM_RESET=y
+CONFIG_SPL_RESET_ROCKCHIP=y
+CONFIG_BAUDRATE=1500000
+CONFIG_DEBUG_UART_BASE=0xFEB50000
+CONFIG_DEBUG_UART_CLOCK=24000000
+CONFIG_DEBUG_UART_SHIFT=2
+CONFIG_ROCKCHIP_SPI=y
+CONFIG_ROCKCHIP_SFC=y
+CONFIG_SYSRESET=y
+CONFIG_USB=y
+CONFIG_USB_XHCI_HCD=y
+CONFIG_USB_XHCI_DWC3=y
+CONFIG_USB_EHCI_HCD=y
+CONFIG_USB_EHCI_GENERIC=y
+CONFIG_USB_OHCI_HCD=y
+CONFIG_USB_OHCI_GENERIC=y
+CONFIG_USB_DWC3=y
+CONFIG_USB_DWC3_GADGET=y
+CONFIG_USB_DWC3_GENERIC=y
+CONFIG_USB_STORAGE=y
+CONFIG_USB_GADGET=y
+CONFIG_USB_GADGET_MANUFACTURER="Rockchip"
+CONFIG_USB_GADGET_VENDOR_NUM=0x2207
+CONFIG_USB_GADGET_PRODUCT_NUM=0x350a
+CONFIG_USB_GADGET_DOWNLOAD=y
+CONFIG_DM_VIDEO=y
+CONFIG_DISPLAY=y
+CONFIG_DRM_ROCKCHIP=y
+CONFIG_DRM_MAXIM_MAX96745=y
+CONFIG_DRM_MAXIM_MAX96755F=y
+CONFIG_DRM_PANEL_MAXIM_MAX96752F=y
+CONFIG_DRM_ROHM_BU18XL82=y
+CONFIG_DRM_ROCKCHIP_DW_HDMI_QP=y
+CONFIG_DRM_ROCKCHIP_DW_MIPI_DSI2=y
+CONFIG_DRM_ROCKCHIP_DW_DP=y
+CONFIG_DRM_ROCKCHIP_ANALOGIX_DP=y
+CONFIG_DRM_ROCKCHIP_SAMSUNG_MIPI_DCPHY=y
+CONFIG_PHY_ROCKCHIP_SAMSUNG_HDPTX_HDMI=y
+CONFIG_USE_TINY_PRINTF=y
+CONFIG_LIB_RAND=y
+CONFIG_SPL_TINY_MEMSET=y
+CONFIG_RSA=y
+CONFIG_SPL_RSA=y
+CONFIG_RSA_N_SIZE=0x200
+CONFIG_RSA_E_SIZE=0x10
+CONFIG_RSA_C_SIZE=0x20
+CONFIG_XBC=y
+CONFIG_LZ4=y
+CONFIG_LZMA=y
+CONFIG_ERRNO_STR=y
+# CONFIG_EFI_LOADER is not set
+CONFIG_AVB_LIBAVB=y
+CONFIG_AVB_LIBAVB_AB=y
+CONFIG_AVB_LIBAVB_ATX=y
+CONFIG_AVB_LIBAVB_USER=y
+CONFIG_RK_AVB_LIBAVB_USER=y
+CONFIG_CHECK_VERSION_CHOOSE_DTB=y
-- 
2.25.1

