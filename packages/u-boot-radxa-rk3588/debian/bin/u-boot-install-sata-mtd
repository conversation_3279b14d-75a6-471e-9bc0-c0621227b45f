#!/bin/bash

set -eE 

disk="/dev/mtd0"
if [ ! -c "${disk}" ]; then
    echo "$(basename "$0"): block device '${disk}' not found"
    exit 1
fi
echo "$(basename "$0"): writing U-Boot to the SPI flash"

uboot_spi_image="/usr/lib/u-boot/u-boot-rockchip-spi-sata.bin"
if [ ! -f "${uboot_spi_image}" ]; then
    uboot_spi_image="/usr/lib/u-boot/rkspi_loader_sata.img"
fi

extra_opts_flashcp=("--verbose")
if flashcp -h | grep -q -e '--partition'; then
    extra_opts_flashcp+=("--partition")
fi
sudo flashcp "${extra_opts_flashcp[@]}" "${uboot_spi_image}" "${disk}"

sudo sync
