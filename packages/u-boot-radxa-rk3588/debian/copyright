  U-Boot is Free Software.  It is copyrighted by <PERSON> and
many others who contributed code (see the actual source code and the
git commit messages for details).  You can redistribute U-Boot and/or
modify it under the terms of version 2 of the GNU General Public
License as published by the Free Software Foundation.  Most of it can
also be distributed, at your option, under any later version of the
GNU General Public License -- see individual files for exceptions.

  NOTE! This license does *not* cover the so-called "standalone"
applications that use U-Boot services by means of the jump table
provided by U-Boot exactly for this purpose - this is merely
considered normal use of U-Boot, and does *not* fall under the
heading of "derived work" -- see file  Licenses/Exceptions  for
details.

  Also note that the GPL and the other licenses are copyrighted by
the Free Software Foundation and other organizations, but the
instance of code that they refer to (the U-Boot source code) is
copyrighted by me and others who actually wrote it.
-- <PERSON>


Like many other projects, U-Boot has a tradition of including big
blocks of License headers in all files.  This not only blows up the
source code with mostly redundant information, but also makes it very
difficult to generate License Clearing Reports.  An additional problem
is that even the same licenses are referred to by a number of
slightly varying text blocks (full, abbreviated, different
indentation, line wrapping and/or white space, with obsolete address
information, ...) which makes automatic processing a nightmare.

To make this easier, such license headers in the source files will be
replaced with a single line reference to Unique License Identifiers
as defined by the Linux Foundation's SPDX project [1].  For example,
in a source file the full "GPL v2.0 or later" header text will be
replaced by a single line:

	SPDX-License-Identifier:	GPL-2.0+

Ideally, the license terms of all files in the source tree should be
defined by such License Identifiers; in no case a file can contain
more than one such License Identifier list.

If a "SPDX-License-Identifier:" line references more than one Unique
License Identifier, then this means that the respective file can be
used under the terms of either of these licenses, i. e. with

	SPDX-License-Identifier:	GPL-2.0+	BSD-3-Clause

you can choose between GPL-2.0+ and BSD-3-Clause licensing.

We use the SPDX Unique License Identifiers here; these are available
at [2].

[1] http://spdx.org/
[2] http://spdx.org/licenses/

Full name					SPDX Identifier	OSI Approved	File name		URI
=======================================================================================================================================
GNU General Public License v2.0 only		GPL-2.0		Y		gpl-2.0.txt		http://www.gnu.org/licenses/gpl-2.0.txt
GNU General Public License v2.0 or later	GPL-2.0+	Y		gpl-2.0.txt		http://www.gnu.org/licenses/gpl-2.0.txt
GNU Library General Public License v2 or later	LGPL-2.0+	Y		lgpl-2.0.txt		http://www.gnu.org/licenses/old-licenses/lgpl-2.0.txt
GNU Lesser General Public License v2.1 or later	LGPL-2.1+	Y		lgpl-2.1.txt		http://www.gnu.org/licenses/old-licenses/lgpl-2.1.txt
eCos license version 2.0			eCos-2.0			eCos-2.0.txt		http://www.gnu.org/licenses/ecos-license.html
BSD 2-Clause License				BSD-2-Clause	Y		bsd-2-clause.txt	http://spdx.org/licenses/BSD-2-Clause
BSD 3-clause "New" or "Revised" License		BSD-3-Clause	Y		bsd-3-clause.txt	http://spdx.org/licenses/BSD-3-Clause#licenseText
IBM PIBS (PowerPC Initialization and		IBM-pibs			ibm-pibs.txt
	Boot Software) license
ISC License					ISC		Y		isc.txt			https://spdx.org/licenses/ISC
SIL OPEN FONT LICENSE (OFL-1.1)			OFL-1.1		Y		OFL.txt			https://spdx.org/licenses/OFL-1.1.html
X11 License					X11				x11.txt			https://spdx.org/licenses/X11.html