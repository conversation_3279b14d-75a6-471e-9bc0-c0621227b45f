u-boot-radxa-rk3588 (2017.09+20240806.gitf73b1eed-2) jammy; urgency=medium

  * Revert "rockchip: resource: Add avb verify"

 -- <PERSON> <<EMAIL>>  Fri, 30 Aug 2024 17:17:30 -0400

u-boot-radxa-rk3588 (2017.09+20240806.gitf73b1eed-1) jammy; urgency=medium

  * configs: e25: change CONFIG_BAUDRATE 1500000 to 115200
  * dts: radxa-e25: add blue led node
  * radxa-e25: blue light on when entering loader mode
  * BACKPORT: distro/pxeboot: Handle prompt variable
  * board: rockchip: evb_rk3528: Add hardware ID recognition for mEdge-RK3528A IO
  * arm: dts: add radxa-e52c
  * configs: add radxa-e52c-rk3588s_defconfig
  * common: fix type of cmd_process for -Werror=enum-int-mismatch
  * edid: add_cea_modes: fix build with -Werror=maybe-uninitialized
  * dts: rock5b+: enable pcie3x2

 -- <PERSON> <<EMAIL>>  <PERSON><PERSON>, 20 Aug 2024 20:28:47 -0400

u-boot-radxa-rk3588 (2017.09+20240626.git5202c429-5) jammy; urgency=medium

  * Fix booting from SPI NOR on the Orange Pi 5
  * Fix eMMC not detected in SPL when booting from SPI NOR

 -- Joshua Riek <<EMAIL>>  Thu, 08 Aug 2024 23:07:36 -0400

u-boot-radxa-rk3588 (2017.09+20240626.git5202c429-4) jammy; urgency=medium

  * Add support for the Orange Pi 5 CM5 and 5 Max

 -- Joshua Riek <<EMAIL>>  Mon, 29 Jul 2024 20:20:44 -0400

u-boot-radxa-rk3588 (2017.09+20240626.git5202c429-3) jammy; urgency=medium

  * Fix filter-out makefile rule

 -- Joshua Riek <<EMAIL>>  Sun, 14 Jul 2024 11:26:58 -0400

u-boot-radxa-rk3588 (2017.09+20240626.git5202c429-2) jammy; urgency=medium

  * Add support for Orange Pi 5 boards

 -- Joshua Riek <<EMAIL>>  Sun, 14 Jul 2024 10:49:38 -0400

u-boot-radxa-rk3588 (2017.09+20240626.git5202c429-1) jammy; urgency=medium

  * Bump to next-dev-v2024.03

 -- Joshua Riek <<EMAIL>>  Sat, 29 Jun 2024 19:09:11 -0400

u-boot-radxa-rk3588 (2017.09+20240313.gitcdbe95ed-3) jammy; urgency=medium

  * Update ddr and bl31 blobs for all boards
  * Add E-Key slot nvme boot support for NanoPC T6
  * Update U-Boot install scripts

 -- Joshua Riek <<EMAIL>>  Wed, 12 Jun 2024 18:42:39 -0400

u-boot-radxa-rk3588 (2017.09+20240313.gitcdbe95ed-2) jammy; urgency=medium

  * update ddr and spl blobs
  * enable the radxa cm5 rpi cm4 io

 -- Joshua Riek <<EMAIL>>  Tue, 04 Jun 2024 17:33:36 -0400

u-boot-radxa-rk3588 (2017.09+20240313.gitcdbe95ed-1) jammy; urgency=medium

  * enable the rock 5b plus
  * enable the rock 5c
  * enable the rock 5d
  * rockchip: rk3588: Rename rk3582 functions
  * rockchip: rk3588: Add rk3583 support
  * rock-5-itx: force to boot from eMMC when recovery pin is shorted to ground
  * configs: cm3i-io: update defconfig
  * rockchip: rk3568: enable CONFIG_MISC_INIT_R
  * arm: dts: rk3568-u-boot: add otp and cpuinfo
  * arm: dts: rock 5c: add recovery key
  * defconfig: add rock 5c defconfig
  * arm: dts: add rock 5c
  * configs: rock-5b: Disable usb boot option to speed up startup

 -- Joshua Riek <<EMAIL>>  Sat, 25 May 2024 11:42:00 -0400

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-6) jammy; urgency=medium

  * Refactor armsom pd patch

 -- Joshua Riek <<EMAIL>>  Sat, 27 Apr 2024 12:26:15 -0400

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-5) jammy; urgency=medium

  * move config preboot into the armsom defconfig

 -- Joshua Riek <<EMAIL>>  Tue, 26 Mar 2024 19:39:13 -0400

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-4) jammy; urgency=medium

  * Add PD negotiation for the armsom sige 7

 -- Joshua Riek <<EMAIL>>  Tue, 26 Mar 2024 19:24:39 -0400

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-3) jammy; urgency=medium

  * Use flashcp to update spi flash

 -- Joshua Riek <<EMAIL>>  Mon, 25 Mar 2024 14:07:42 -0400

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-2) jammy; urgency=medium

  * Fix gcc 13 compile error

 -- Joshua Riek <<EMAIL>>  Sun, 11 Feb 2024 20:51:36 -0500

u-boot-radxa-rk3588 (2017.09+20240128.git722a6ab0-1) jammy; urgency=medium

  * arm: dts: add radxa e25 support
  * arm: dts: add radxa cm3i-io support
  * Merge pull request #56 from vamrs-feng/next-dev
  * arm: dts: rock 5b plus: Fix for missing nodes
  * Merge pull request #55 from vamrs-feng/next-dev
  * rock-5b-plus-rk3588_defconfig: support usb boot
  * arm: dts: rock 5b plus: Configure usb port
  * radxa-nx5-io-rk3588s_defconfig: support usb boot
  * arm: dts: radxa nx5 io: Configure usb port
  * rock-5-itx-rk3588_defconfig: support usb boot
  * arm: dts: rock 5 itx: Configure usb port
  * rock-5b-rk3588_defconfig: support usb boot
  * arm: dts: rock 5b: Configure usb port

 -- Joshua Riek <<EMAIL>>  Sun, 28 Jan 2024 14:17:24 -0500

u-boot-radxa-rk3588 (2017.09+20231220.git84dc6c96-1) jammy; urgency=medium

  * Squash u-boot source code into radxa package

 -- Joshua Riek <<EMAIL>>  Sat, 23 Dec 2023 19:27:47 -0500

u-boot-radxa-rk3588 (2017.09+20231108.git957d61fa-1) jammy; urgency=medium

  * Add build for the ROCK 5 ITX and NX5 io
  * Add dts for the ROCK 5 ITX
  * Merge pull request #40 from Ken-Vamrs/next-dev
  * arm: dts: rock5b plus: split pcie 3.0 into two pcie
  * configs: add rock-5b-plus-rk3588_defconfig
  * arm: dts: add rock 5b plus
  * Merge pull request #36 from Ken-Vamrs/next-dev
  * rockchip: radxa-cm5-rpi-cm4-io: update defconfig
  * rockchip: radxa-cm5-io: update defconfig
  * Merge pull request #35 from vamrs-feng/next-dev
  * rockchip: rk3328: enable CONFIG_MISC_INIT_R
  * rockchip: rk3328-rock-pi-e: update defconfig
  * rockchip: radxa-nx5-io: update defconfig
  * rockchip: rk3588s-rock-5a: update defconfig
  * rockchip: rk3588: derive ethaddr from cpuid

 -- Joshua Riek <<EMAIL>>  Sat, 16 Dec 2023 17:45:18 -0500

u-boot-radxa-rk3588 (2017.09+20230920.git609a77ef-3) jammy; urgency=medium

  * Add u-boot install scripts

 -- Joshua Riek <<EMAIL>>  Thu, 26 Oct 2023 17:52:34 -0400

u-boot-radxa-rk3588 (2017.09+20230920.git609a77ef-2) jammy; urgency=medium

  * Update u-boot install path

 -- Joshua Riek <<EMAIL>>  Fri, 20 Oct 2023 21:11:44 -0400

u-boot-radxa-rk3588 (2017.09+20230920.git609a77ef-1) jammy; urgency=medium

  * configs: add radxa-cm5-rpi-cm4-io-rk3588s_defconfig
  * arm: dts: add rk3588s radxa cm5 rpi cm4 io support
  * HACK: rockchip: rkimg: enter download mode when pressing recovery button
  * configs: rock-pi-e-rk3328_defconfig
  * arm: dts: add rock pi e
  * rockchip: rk3328: define fdtfile and fdtoverlay_addr_r variable

 -- Joshua Riek <<EMAIL>>  Wed, 20 Sep 2023 20:36:50 -0400

u-boot-radxa-rk3588 (2017.09+20230711.gitddc91cd0-3) jammy; urgency=medium

  * Add ddr and bl31 blobs again

 -- Joshua Riek <<EMAIL>>  Fri, 11 Aug 2023 17:53:16 -0400

u-boot-radxa-rk3588 (2017.09+20230711.gitddc91cd0-2) jammy; urgency=medium

  * Revert ddr and bl31 blob update

 -- Joshua Riek <<EMAIL>>  Fri, 11 Aug 2023 11:39:32 -0400

u-boot-radxa-rk3588 (2017.09+20230711.gitddc91cd0-1) jammy; urgency=medium

  * Updated the ddr blob to rk3588_ddr_lp4_2112MHz_lp5_2736MHz_v1.11.bin
  * Updated the bl31 blob to rk3588_bl31_v1.38.elf 

 -- Joshua Riek <<EMAIL>>  Thu, 10 Aug 2023 01:33:57 -0400

u-boot-radxa-rk3588 (2017.09+20230711.gitddc91cd0) jammy; urgency=medium

  * renamed rock-5a and rock-5b package
  * added package for radxa-cm5-io

  * upstream changes
    - Merge pull request #27 from Ken-Vamrs/next-dev
    - rockchip: add ROCK 4 boards
    - rockchip: rk3399: define fdtfile variable
    - power: regulator: fan53555: add rk860 calibration
    - common: fix buidl error
    - configs: add rock-3a-rk3568_defconfig
    - arm64: dts: add ROCK 3A board
    - arm: dts: add radxa cm5 io
    - configs: add radxa-cm5-io-rk3588s_defconfig

 -- Joshua Riek <<EMAIL>>  Fri, 14 Jul 2023 18:13:31 -0400

u-boot-radxa-rk3588 (2017.09+20230607.gitbf47e817-1) jammy; urgency=medium

  * rockchip: rk3588: Set emmc iomux for better extention
  * configs: add defconfig for rock5a with spi flash
  * rockchip: dts: add dts for rock-5a with spi flash
  * rockchip: dts: rk3588s rock 5a: add pcie2x1l2
  * configs: rock 5a: enable phy for pcie2
  * give SD higher boot priority than NVME

 -- Joshua Riek <<EMAIL>>  Tue, 13 Jun 2023 09:03:27 -0400

u-boot-radxa-rk3588 (2017.09+20230413.gita2a9f1df-1) jammy; urgency=medium

  * Initial packaging.

 -- Joshua Riek <<EMAIL>>  Tue, 16 May 2023 09:04:54 -0400
