#!/bin/bash

set -eE 
trap 'echo Error: in $0 on line $LINENO' ERR

cd "$(dirname -- "$(readlink -f -- "$0")")" && cd ../..

# Current upstream commit and version
. debian/upstream

orig_tarball=../u-boot-radxa-rk3588_${VERSION}.orig.tar.xz
if [ -e "${orig_tarball}" ] ; then
    echo "Orig tarball ${orig_tarball} exists already"
    exit
fi

echo "Creating orig tarball ${orig_tarball} ..."
git archive --format=tar --prefix=u-boot-radxa-rk3588-"${VERSION}"/ "${COMMIT}" | XZ_OPT="-0 -T0" xz -3 -T0 -z - > "${orig_tarball}"
