#!/bin/sh

# Remove the old u-boot path so we can replace it with a symlink
path="/usr/lib/u-boot-radxa-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-friendlyelec-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-lubancat-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-9tripod-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-firefly-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-mixtile-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

path="/usr/lib/u-boot-orangepi-rk3588/"
if [ -d ${path} ] && [ ! -L ${path} ]; then
    rm -rf ${path}
fi

exit 0
