Source: u-boot-radxa-rk3588
Section: admin
Priority: optional
Standards-Version: 4.6.0
Maintainer: <PERSON> <<EMAIL>>
Build-Depends: debhelper-compat (= 12), parted, gcc, make, bc, bison, python3, flex, device-tree-compiler, udev
Vcs-Git: https://github.com/radxa/u-boot.git

Package: u-boot-rock-5d
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5D.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-rock-5c
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5C.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
 
Package: u-boot-rock-5b
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5B.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-rock-5b-plus
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5B Plus.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-rock-5a
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5A.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-rock-5-itx
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa ROCK 5 ITX.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-radxa-cm5-io
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa CM5 IO.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-radxa-cm5-rpi-cm4-io
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa CM5 IO.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-radxa-nx5-io
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-radxa-rk3588, u-boot
Replaces: u-boot-radxa-rk3588, u-boot
Conflicts: u-boot-radxa-rk3588, u-boot
Description: A boot loader for the Radxa NX5 IO.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-nanopi-r6s
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-friendlyelec-rk3588, u-boot
Replaces: u-boot-friendlyelec-rk3588, u-boot
Conflicts: u-boot-friendlyelec-rk3588, u-boot
Description: A boot loader for the FriendlyELEC NanoPi R6S.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-nanopi-r6c
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-friendlyelec-rk3588, u-boot
Replaces: u-boot-friendlyelec-rk3588, u-boot
Conflicts: u-boot-friendlyelec-rk3588, u-boot
Description: A boot loader for the FriendlyELEC NanoPi R6C.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-nanopc-t6
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-friendlyelec-rk3588, u-boot
Replaces: u-boot-friendlyelec-rk3588, u-boot
Conflicts: u-boot-friendlyelec-rk3588, u-boot
Description: A boot loader for the FriendlyELEC NanoPC T6.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-lubancat-4
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-lubancat-rk3588, u-boot
Replaces: u-boot-lubancat-rk3588, u-boot
Conflicts: u-boot-lubancat-rk3588, u-boot
Description: A boot loader for the lubancat 4.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-indiedroid-nova
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-9tripod-rk3588, u-boot
Replaces: u-boot-9tripod-rk3588, u-boot
Conflicts: u-boot-9tripod-rk3588, u-boot
Description: A boot loader for the Indiedroid Nova.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-armsom-w3
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-armsom-rk3588, u-boot
Replaces: u-boot-armsom-rk3588, u-boot
Conflicts: u-boot-armsom-rk3588, u-boot
Description: A boot loader for the ArmSoM w3.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
 
Package: u-boot-armsom-sige7
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-armsom-rk3588, u-boot
Replaces: u-boot-armsom-rk3588, u-boot
Conflicts: u-boot-armsom-rk3588, u-boot
Description: A boot loader for the ArmSoM Sige7.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-armsom-aim7
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-armsom-rk3588, u-boot
Replaces: u-boot-armsom-rk3588, u-boot
Conflicts: u-boot-armsom-rk3588, u-boot
Description: A boot loader for the ArmSoM AIM7.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-roc-rk3588s-pc
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-firefly-rk3588, u-boot
Replaces: u-boot-firefly-rk3588, u-boot
Conflicts: u-boot-firefly-rk3588, u-boot
Description: A boot loader for the Firefly ROC RK3588S PC.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.


Package: u-boot-aio-3588l
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-firefly-rk3588, u-boot
Replaces: u-boot-firefly-rk3588, u-boot
Conflicts: u-boot-firefly-rk3588, u-boot
Description: A boot loader for the Firefly AIO 3588L.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-mixtile-core3588e
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-mixtile-rk3588, u-boot
Replaces: u-boot-mixtile-rk3588, u-boot
Conflicts: u-boot-mixtile-rk3588, u-boot
Description: A boot loader for the Mixtile Core 3588E.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-5
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-5b
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5B.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-5-pro
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5 Pro.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-5-max
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5 Max.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-5-plus
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5 Plus.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.

Package: u-boot-orangepi-cm5
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-orangepi-rk3588, u-boot
Replaces: u-boot-orangepi-rk3588, u-boot
Conflicts: u-boot-orangepi-rk3588, u-boot
Description: A boot loader for the Orange Pi 5 CM5.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
