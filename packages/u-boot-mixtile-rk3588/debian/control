Source: u-boot-mixtile-rk3588
Section: admin
Priority: optional
Standards-Version: 4.6.0
Maintainer: <PERSON> <<EMAIL>>
Build-Depends: debhelper-compat (= 12), parted, gcc, make, bc, bison, python3, flex, device-tree-compiler, udev
Vcs-Git: https://github.com/radxa/u-boot.git

Package: u-boot-mixtile-blade3
Architecture: arm64
Priority: optional
Depends: mtd-utils
Provides: u-boot-mixtile-rk3588, u-boot
Replaces: u-boot-mixtile-rk3588, u-boot
Conflicts: u-boot-mixtile-rk3588, u-boot
Description: A boot loader for the Mixtile Blade 3.
 Das U-Boot is a cross-platform bootloader for embedded systems,
 used as the default boot loader by several board vendors.  It is
 intended to be easy to port and to debug, and runs on many
 supported architectures, including PPC, ARM, MIPS, x86, m68k,
 NIOS, and Microblaze.
