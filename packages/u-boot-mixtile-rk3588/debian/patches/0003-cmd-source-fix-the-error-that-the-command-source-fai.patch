From b58348c681a2e14bc177698a594d28bb5ce1297b Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 30 Jun 2023 00:02:51 +0200
Subject: [PATCH 3/7] cmd: source: fix the error that the command source failed
 to execute

---
 cmd/source.c | 2 +-
 1 file changed, 1 insertion(+), 1 deletion(-)

diff --git a/cmd/source.c b/cmd/source.c
index 6b1c8b744b..cf820c072a 100644
--- a/cmd/source.c
+++ b/cmd/source.c
@@ -87,7 +87,7 @@ source (ulong addr, const char *fit_uname)
 		 * past the zero-terminated sequence of image lengths to get
 		 * to the actual image data
 		 */
-		while (*data++ != IMAGE_PARAM_INVAL);
+		while (*data++);
 		break;
 #endif
 #if defined(CONFIG_FIT)
-- 
2.25.1

