From 237a48e45da42d5fc24530c51e26faef2f7e6778 Mon Sep 17 00:00:00 2001
From: tom <<EMAIL>>
Date: Thu, 18 May 2023 17:35:31 +0800
Subject: [PATCH 2/7] fix power_delivery driver affects adb to enter loader
 mode.

---
 arch/arm/mach-rockchip/board.c | 9 ++++++++-
 1 file changed, 8 insertions(+), 1 deletion(-)

diff --git a/arch/arm/mach-rockchip/board.c b/arch/arm/mach-rockchip/board.c
index e60153dde8..9b2450ec90 100644
--- a/arch/arm/mach-rockchip/board.c
+++ b/arch/arm/mach-rockchip/board.c
@@ -424,7 +424,14 @@ static void power_delivery_func(void)
 
 int board_late_init(void)
 {
-	power_delivery_func();
+	int boot_mode;
+	boot_mode = readl((void *)CONFIG_ROCKCHIP_BOOT_MODE_REG);
+	if (boot_mode == BOOT_MODE_NORMAL ||
+			boot_mode == BOOT_MODE_RECOVERY ||
+			boot_mode == BOOT_MODE_UNDEFINE) {
+		power_delivery_func();
+	}
+
 #ifdef CONFIG_ROCKCHIP_SET_ETHADDR
 	rockchip_set_ethaddr();
 #endif
-- 
2.25.1

