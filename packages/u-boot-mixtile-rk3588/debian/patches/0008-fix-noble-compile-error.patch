From 6ed03d02f96b28cb14fe78b5b6cb24f3fc63a5cf Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Fri, 23 Feb 2024 13:54:20 -1000
Subject: [PATCH] foo

---
 Makefile | 3 +++
 1 file changed, 3 insertions(+)

diff --git a/Makefile b/Makefile
index 590fd4c1..427cb75c 100644
--- a/Makefile
+++ b/Makefile
@@ -389,6 +389,9 @@ export RCS_FIND_IGNORE := \( -name SCCS -o -name BitKeeper -o -name .svn -o    \
 			  -prune -o
 export RCS_TAR_IGNORE := --exclude SCCS --exclude Bit<PERSON>eeper --exclude .svn \
 			 --exclude CVS --exclude .pc --exclude .hg --exclude .git
+KBUILD_CFLAGS += $(call cc-option,-Wno-address)
+KBUILD_CFLAGS += $(call cc-option,-Wno-maybe-uninitialized)
+KBUILD_CFLAGS += $(call cc-option,-Wno-enum-int-mismatch)
 
 # ===========================================================================
 # Rules shared between *config targets and build targets
-- 
2.43.0

