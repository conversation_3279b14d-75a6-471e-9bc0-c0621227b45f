From 8dc2bfa8af0f0572f473773bea33b40d0a5e3293 Mon Sep 17 00:00:00 2001
From: amazingfate <liu<PERSON><PERSON><PERSON><EMAIL>>
Date: Sat, 20 May 2023 00:19:57 +0800
Subject: [PATCH 5/7] give SD higher boot priority than NVME

---
 include/configs/rockchip-common.h | 28 ++++++++++++++--------------
 1 <USER> <GROUP>, 14 insertions(+), 14 deletions(-)

diff --git a/include/configs/rockchip-common.h b/include/configs/rockchip-common.h
index c0402b1fab..f57545826c 100644
--- a/include/configs/rockchip-common.h
+++ b/include/configs/rockchip-common.h
@@ -58,13 +58,20 @@
 	BOOT_TARGET_DEVICES_references_MTD_without_CONFIG_CMD_MTD_BLK
 #endif
 
-/* First try to boot from SD (index 1), then eMMC (index 0) */
+/* First try to boot from SD (index 1), then NVME (if CMD_NVME is enabled), then eMMC (index 0) */
 #if CONFIG_IS_ENABLED(CMD_MMC)
-	#define BOOT_TARGET_MMC(func) \
+#if CONFIG_IS_ENABLED(CMD_NVME)
+	#define BOOT_TARGET_NVME_MMC(func) \
+		func(MMC, mmc, 1) \
+		func(NVME, nvme, 0) \
+		func(MMC, mmc, 0)
+#else
+	#define BOOT_TARGET_NVME_MMC(func) \
 		func(MMC, mmc, 1) \
 		func(MMC, mmc, 0)
+#endif
 #else
-	#define BOOT_TARGET_MMC(func)
+	#define BOOT_TARGET_NVME_MMC(func)
 #endif
 
 #if CONFIG_IS_ENABLED(CMD_MTD_BLK)
@@ -82,12 +89,6 @@
 	#define BOOT_TARGET_RKNAND(func)
 #endif
 
-#if CONFIG_IS_ENABLED(CMD_NVME)
-	#define BOOT_TARGET_NVME(func) func(NVME, nvme, 0)
-#else
-	#define BOOT_TARGET_NVME(func)
-#endif
-
 #if CONFIG_IS_ENABLED(CMD_USB)
 	#define BOOT_TARGET_USB(func) func(USB, usb, 0)
 #else
@@ -107,8 +108,7 @@
 #endif
 
 #define BOOT_TARGET_DEVICES(func) \
-	BOOT_TARGET_NVME(func) \
-	BOOT_TARGET_MMC(func) \
+	BOOT_TARGET_NVME_MMC(func) \
 	BOOT_TARGET_MTD(func) \
 	BOOT_TARGET_RKNAND(func) \
 	BOOT_TARGET_USB(func) \
@@ -156,10 +156,10 @@
 
 #define RKIMG_DET_BOOTDEV \
 	"rkimg_bootdev=" \
-	"if nvme dev 0; then " \
-		"setenv devtype nvme; setenv devnum 0; echo Boot from nvme;" \
-	"elif mmc dev 1 && rkimgtest mmc 1; then " \
+	"if mmc dev 1 && rkimgtest mmc 1; then " \
 		"setenv devtype mmc; setenv devnum 1; echo Boot from SDcard;" \
+	"elif nvme dev 0; then " \
+		"setenv devtype nvme; setenv devnum 0; echo Boot from nvme;" \
 	"elif mmc dev 0; then " \
 		"setenv devtype mmc; setenv devnum 0;" \
 	"elif mtd_blk dev 0; then " \
-- 
2.25.1

