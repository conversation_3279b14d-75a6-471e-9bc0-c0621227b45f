#!/bin/bash

set -eE
trap 'echo Error: in $0 on line $LINENO' ERR

# Script to enable eBPF kernel configuration options
# This script modifies the kernel configuration to enable eBPF support

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Enable eBPF kernel configuration options for Ubuntu Rockchip kernel"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -c, --config FILE   Specify kernel config file (default: auto-detect)"
    echo "  -d, --dry-run       Show what would be changed without making changes"
    echo "  -v, --verbose       Enable verbose output"
    echo ""
    echo "Environment variables:"
    echo "  SUITE               Ubuntu suite (jammy, noble, etc.)"
    echo ""
    echo "Examples:"
    echo "  SUITE=jammy $0                    # Enable eBPF for jammy kernel"
    echo "  $0 -c /path/to/.config -d         # Dry run with specific config"
    echo "  $0 -v                             # Verbose mode"
}

# Default values
DRY_RUN=false
VERBOSE=false
CONFIG_FILE=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            exit 0
            ;;
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Check if running as root (except for help)
if [ "$(id -u)" -ne 0 ]; then
    echo "Please run as root"
    exit 1
fi

# Function to check build dependencies
check_build_dependencies() {
    local missing_deps=()

    # Check for libelf development library
    if ! dpkg -l | grep -q "libelf-dev"; then
        missing_deps+=("libelf-dev")
    fi

    # Check for zlib development library
    if ! dpkg -l | grep -q "zlib1g-dev"; then
        missing_deps+=("zlib1g-dev")
    fi

    # Check for cross-compilation tools
    if ! command -v aarch64-linux-gnu-gcc >/dev/null 2>&1; then
        missing_deps+=("gcc-aarch64-linux-gnu")
    fi

    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo "Warning: Missing build dependencies for eBPF kernel compilation:"
        printf "  - %s\n" "${missing_deps[@]}"
        echo ""
        echo "Install them with:"
        echo "  sudo apt update"
        echo "  sudo apt install -y ${missing_deps[*]}"
        echo ""
        if [ "$DRY_RUN" = false ]; then
            read -p "Continue anyway? (y/N): " -n 1 -r
            echo
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                exit 1
            fi
        fi
    fi
}

# Verbose logging function
log_verbose() {
    if [ "$VERBOSE" = true ]; then
        echo "[VERBOSE] $*"
    fi
}

# Change to script directory and then to project root
cd "$(dirname -- "$(readlink -f -- "$0")")" && cd ..

# Check if SUITE is set
if [[ -z ${SUITE} ]]; then
    echo "Error: SUITE is not set"
    echo "Please set SUITE environment variable (e.g., SUITE=jammy)"
    exit 1
fi

# Source the suite configuration
if [[ ! -f "config/suites/${SUITE}.sh" ]]; then
    echo "Error: Suite configuration file 'config/suites/${SUITE}.sh' not found"
    exit 1
fi

# shellcheck source=/dev/null
source "config/suites/${SUITE}.sh"

log_verbose "Using suite: ${SUITE}"
log_verbose "Kernel repo: ${KERNEL_REPO}"
log_verbose "Kernel branch: ${KERNEL_BRANCH}"

# Create build directory and navigate to it
mkdir -p build && cd build

# Check if kernel source exists
if [[ ! -d "linux-rockchip" ]]; then
    echo "Error: Kernel source directory 'linux-rockchip' not found"
    echo "Please run the kernel build script first to clone the kernel source"
    exit 1
fi

cd linux-rockchip

# Checkout the correct branch
git checkout "${KERNEL_BRANCH}"

# Set up cross-compilation environment
# shellcheck disable=SC2046
export $(dpkg-architecture -aarm64)
export CROSS_COMPILE=aarch64-linux-gnu-
export CC=aarch64-linux-gnu-gcc
export LANG=C

# Determine config file location
if [[ -n "$CONFIG_FILE" ]]; then
    if [[ ! -f "$CONFIG_FILE" ]]; then
        echo "Error: Specified config file '$CONFIG_FILE' not found"
        exit 1
    fi
    KERNEL_CONFIG="$CONFIG_FILE"
else
    # Auto-detect kernel config file
    if [[ -f ".config" ]]; then
        KERNEL_CONFIG=".config"
    elif [[ -f "arch/arm64/configs/defconfig" ]]; then
        KERNEL_CONFIG="arch/arm64/configs/defconfig"
    elif [[ -f "arch/arm64/configs/rockchip_linux_defconfig" ]]; then
        KERNEL_CONFIG="arch/arm64/configs/rockchip_linux_defconfig"
    else
        echo "Error: Could not find kernel configuration file"
        echo "Please specify config file with -c option or ensure kernel is configured"
        exit 1
    fi
fi

log_verbose "Using kernel config file: $KERNEL_CONFIG"

# Define eBPF configuration options
declare -A EBPF_CONFIGS=(
    # Core BPF options
    ["CONFIG_BPF"]="y"
    ["CONFIG_BPF_JIT"]="y"
    ["CONFIG_BPF_JIT_DEFAULT_ON"]="y"
    ["CONFIG_BPF_EVENTS"]="y"
    ["CONFIG_BPF_SYSCALL"]="y"
    ["CONFIG_HAVE_BPF_JIT"]="y"
    ["CONFIG_HAVE_EBPF_JIT"]="y"
    ["CONFIG_FTRACE_SYSCALLS"]="y"
    
    # BTF (BPF Type Format) support
    ["CONFIG_DEBUG_INFO_BTF"]="y"
    ["CONFIG_DEBUG_INFO_BTF_MODULES"]="y"
    
    # Enforcement support
    ["CONFIG_BPF_KPROBE_OVERRIDE"]="y"
    
    # CGROUP and Process tracking
    ["CONFIG_CGROUPS"]="y"
    ["CONFIG_MEMCG"]="y"
    ["CONFIG_BLK_CGROUP"]="y"
    ["CONFIG_CGROUP_SCHED"]="y"
    ["CONFIG_CGROUP_PIDS"]="y"
    ["CONFIG_CGROUP_FREEZER"]="y"
    ["CONFIG_CPUSETS"]="y"
    ["CONFIG_PROC_PID_CPUSET"]="y"
    ["CONFIG_CGROUP_DEVICE"]="y"
    ["CONFIG_CGROUP_CPUACCT"]="y"
    ["CONFIG_CGROUP_PERF"]="y"
    ["CONFIG_CGROUP_BPF"]="y"
    
    # Optional: CGROUPv1 support for kernels >= 6.11
    ["CONFIG_MEMCG_V1"]="y"
    ["CONFIG_CPUSETS_V1"]="y"
    
    # Optional: Reduce cgroup modification latencies
    ["CONFIG_CGROUP_FAVOR_DYNMODS"]="y"
    
    # Additional useful BPF options
    ["CONFIG_NET_CLS_BPF"]="m"
    ["CONFIG_NET_ACT_BPF"]="m"
    ["CONFIG_BPF_STREAM_PARSER"]="y"
    ["CONFIG_LWTUNNEL_BPF"]="y"
)

# Check build dependencies
check_build_dependencies

echo "eBPF Kernel Configuration Script"
echo "================================="
echo "Suite: ${SUITE}"
echo "Config file: ${KERNEL_CONFIG}"
echo "Mode: $([ "$DRY_RUN" = true ] && echo "DRY RUN" || echo "APPLY CHANGES")"
echo ""

# Backup original config if not in dry-run mode
if [ "$DRY_RUN" = false ]; then
    cp "$KERNEL_CONFIG" "${KERNEL_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    echo "Backup created: ${KERNEL_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
fi

# Check current configuration and apply changes
CHANGES_MADE=0
CHANGES_NEEDED=0

echo "Checking eBPF configuration options..."
echo ""

for config_option in "${!EBPF_CONFIGS[@]}"; do
    desired_value="${EBPF_CONFIGS[$config_option]}"
    
    # Check if option exists in config
    if grep -q "^${config_option}=" "$KERNEL_CONFIG" 2>/dev/null; then
        current_value=$(grep "^${config_option}=" "$KERNEL_CONFIG" | cut -d'=' -f2)
        if [ "$current_value" = "$desired_value" ]; then
            log_verbose "✓ $config_option is already set to $desired_value"
        else
            echo "⚠ $config_option: $current_value → $desired_value"
            CHANGES_NEEDED=$((CHANGES_NEEDED + 1))
            
            if [ "$DRY_RUN" = false ]; then
                sed -i "s/^${config_option}=.*/${config_option}=${desired_value}/" "$KERNEL_CONFIG"
                CHANGES_MADE=$((CHANGES_MADE + 1))
            fi
        fi
    elif grep -q "^# ${config_option} is not set" "$KERNEL_CONFIG" 2>/dev/null; then
        echo "⚠ $config_option: not set → $desired_value"
        CHANGES_NEEDED=$((CHANGES_NEEDED + 1))
        
        if [ "$DRY_RUN" = false ]; then
            sed -i "s/^# ${config_option} is not set/${config_option}=${desired_value}/" "$KERNEL_CONFIG"
            CHANGES_MADE=$((CHANGES_MADE + 1))
        fi
    else
        echo "+ $config_option: adding → $desired_value"
        CHANGES_NEEDED=$((CHANGES_NEEDED + 1))
        
        if [ "$DRY_RUN" = false ]; then
            echo "${config_option}=${desired_value}" >> "$KERNEL_CONFIG"
            CHANGES_MADE=$((CHANGES_MADE + 1))
        fi
    fi
done

echo ""
echo "Summary:"
echo "--------"
if [ "$DRY_RUN" = true ]; then
    echo "Changes needed: $CHANGES_NEEDED"
    echo ""
    if [ $CHANGES_NEEDED -gt 0 ]; then
        echo "Run without --dry-run to apply these changes."
    else
        echo "No changes needed. eBPF configuration is already optimal."
    fi
else
    echo "Changes applied: $CHANGES_MADE"
    echo ""
    if [ $CHANGES_MADE -gt 0 ]; then
        echo "✓ eBPF kernel configuration has been updated successfully!"
        echo ""
        echo "Next steps:"
        echo "1. Rebuild the kernel with: SUITE=${SUITE} ./scripts/build-kernel.sh"
        echo "2. Install the new kernel packages"
        echo "3. Reboot to use the new kernel with eBPF support"
        echo ""
        echo "To verify eBPF support after reboot, check:"
        echo "  - /sys/kernel/btf/vmlinux should exist"
        echo "  - /proc/config.gz should contain the eBPF options"
    else
        echo "✓ No changes were needed. eBPF configuration is already optimal."
    fi
fi

exit 0
