name: Release
run-name: Release

on:
  workflow_dispatch:

permissions:
    contents: write

jobs:
  prepare_release:
    name: Create release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Create empty release
        id: release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: v2.4.2
          token: ${{ secrets.GITHUB_TOKEN }}
          target_commitish: main
          draft: true
    outputs:
      release_id: ${{ steps.release.outputs.id }}

  config:
    runs-on: ubuntu-latest
    name: Create martrix
    outputs:
      build_matrix: ${{ steps.build.outputs.build_matrix }}
      rootfs_matrix: ${{ steps.rootfs.outputs.rootfs_matrix }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create build matrix
        id: build
        run: |
            build_matrix=""
            for file in config/boards/*; do
                COMPATIBLE_SUITES=()
                COMPATIBLE_FLAVORS=()
                # shellcheck disable=SC1090
                source "${file}"
                for suite in "${COMPATIBLE_SUITES[@]}"; do
                    for flavor in "${COMPATIBLE_FLAVORS[@]}"; do
                    build_matrix+="{\"board\":\"$(basename "${file%.sh}")\",\"suite\":\"${suite}\",\"flavor\":\"${flavor}\"},"
                    done
                done
            done
            echo "build_matrix={\"include\":[${build_matrix::-1}]}" >> $GITHUB_OUTPUT

      - name: Create rootfs matrix
        id: rootfs
        run: |
            rootfs_matrix=""
            for suite in config/suites/*; do
                for flavor in config/flavors/*; do
                    rootfs_matrix+="{\"suite\":\"$(basename "${suite%.sh}")\",\"flavor\":\"$(basename "${flavor%.sh}")\"},"
                done
            done
            echo "rootfs_matrix={\"include\":[${rootfs_matrix::-1}]}" >> $GITHUB_OUTPUT

  rootfs:
    runs-on: ubuntu-latest
    name: Build rootfs 
    needs: [prepare_release, config]
    strategy:
        matrix: ${{ fromJson(needs.config.outputs.rootfs_matrix) }}
    steps:
      - name: Get more disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: true

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set environment variables
        id: vars
        run: |
          source config/suites/${{ matrix.suite }}.sh
          echo "suite_version=$RELASE_VERSION" >> $GITHUB_OUTPUT

      - name: Install dependencies
        shell: bash
        run: |
          sudo apt-get update && sudo apt-get upgrade -y
          sudo apt-get install -y build-essential gcc-aarch64-linux-gnu bison \
          qemu-user-static qemu-system-arm qemu-efi u-boot-tools binfmt-support \
          debootstrap flex libssl-dev bc rsync kmod cpio xz-utils fakeroot parted \
          udev dosfstools uuid-runtime git-lfs device-tree-compiler python2 python3 \
          python-is-python3 fdisk bc debhelper python3-pyelftools python3-setuptools \
          python3-distutils python3-pkg-resources swig libfdt-dev libpython3-dev dctrl-tools

      - name: Build
        shell: bash
        run: sudo ./build.sh --suite=${{ matrix.suite }} --flavor=${{ matrix.flavor }} --rootfs-only --launchpad

      - name: Upload
        uses: actions/upload-artifact@v4.3.3
        with:
            name: ubuntu-${{ steps.vars.outputs.suite_version }}-preinstalled-${{ matrix.flavor }}-arm64-rootfs
            path: ./build/ubuntu-${{ steps.vars.outputs.suite_version }}-preinstalled-${{ matrix.flavor }}-arm64.rootfs.tar.xz
            if-no-files-found: error

  build:
    runs-on: ubuntu-latest
    needs: [rootfs, prepare_release, config]
    name: Build image 
    strategy:
        matrix: ${{ fromJson(needs.config.outputs.build_matrix) }}
    steps:
      - name: Get more disk space
        uses: jlumbroso/free-disk-space@main
        with:
          tool-cache: false
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          swap-storage: true

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set environment variables
        id: vars
        run: |
          source config/suites/${{ matrix.suite }}.sh
          echo "suite_version=$RELASE_VERSION" >> $GITHUB_OUTPUT

      - name: Checkout rootfs
        uses: actions/download-artifact@v4.1.2
        with:
            name: ubuntu-${{ steps.vars.outputs.suite_version }}-preinstalled-${{ matrix.flavor }}-arm64-rootfs
            path: ./build/

      - name: Install dependencies
        shell: bash
        run: |
          sudo apt-get update && sudo apt-get upgrade -y
          sudo apt-get install -y build-essential gcc-aarch64-linux-gnu bison \
          qemu-user-static qemu-system-arm qemu-efi u-boot-tools binfmt-support \
          debootstrap flex libssl-dev bc rsync kmod cpio xz-utils fakeroot parted \
          udev dosfstools uuid-runtime git-lfs device-tree-compiler python2 python3 \
          python-is-python3 fdisk bc debhelper python3-pyelftools python3-setuptools \
          python3-distutils python3-pkg-resources swig libfdt-dev libpython3-dev dctrl-tools

      - name: Build
        shell: bash
        run: sudo ./build.sh --board=${{ matrix.board }} --suite=${{ matrix.suite }} --flavor=${{ matrix.flavor }} --launchpad

      - name: Upload
        if: needs.prepare_release.outputs.release_id != '' 
        uses: xresloader/upload-to-github-release@v1
        env:
            GITHUB_TOKEN:  ${{ secrets.GITHUB_TOKEN }}
        with:
          release_id: ${{ needs.prepare_release.outputs.release_id }}
          file: images/ubuntu-*-preinstalled-*-arm64-${{ matrix.board }}.*
          draft: true
          overwrite: true

      - name: Clean cache
        shell: bash
        run: sync && sudo rm -rf ./images/ ./build/ && sync
