---
name: Close inactive issues
run-name: Close inactive issues
'on':
  schedule:
    - cron: "0 12 * * 1"

jobs:
  close-issues:
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
    steps:
      - uses: actions/stale@v8
        with:
          operations-per-run: 1000
          days-before-stale: 90
          days-before-close: 90
          stale-issue-label: "stale"
          stale-pr-label: "stale"
          stale-issue-message: |
            This issue has been marked 'stale' due to lack of recent activity. If there is no further activity, the issue will be closed in another 14 days. Thank you for your contribution!
          close-issue-message: |
            This issue has been closed due to inactivity. If you feel this is in error, please reopen the issue or file a new issue with the relevant details.
          stale-pr-message: |
            This pr has been marked 'stale' due to lack of recent activity. If there is no further activity, the issue will be closed in another 14 days. Thank you for your contribution!
          close-pr-message: |
            This pr has been closed due to inactivity. If you feel this is in error, please reopen the issue or file a new issue with the relevant details.
          repo-token: ${{ secrets.GITHUB_TOKEN }}
