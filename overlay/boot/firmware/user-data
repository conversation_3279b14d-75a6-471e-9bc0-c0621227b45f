#cloud-config

# This is the user-data configuration file for cloud-init. By default this sets
# up an initial user called "ubuntu" with password "ubuntu", which must be
# changed at first login. However, many additional actions can be initiated on
# first boot from this file. The cloud-init documentation has more details:
#
# https://cloudinit.readthedocs.io/
#
# Please note that the YAML format employed by this file is sensitive to
# differences in whitespace; if you are editing this file in an editor (like
# Notepad) which uses literal tabs, take care to only use spaces for
# indentation. See the following link for more details:
#
# https://en.wikipedia.org/wiki/YAML
#
# Some additional examples are provided in comments below the default
# configuration.

# The (default) ubuntu user must be in the video group for hardware acceleration
users:
  - default
  - name: ubuntu
    groups: video

# On first boot, set the (default) ubuntu user's password to "ubuntu" and
# expire user passwords
chpasswd:
  expire: true
  users:
  - name: ubuntu
    password: ubuntu
    type: text

## Set the system's hostname. Please note that, unless you have a local DNS
## setup where the hostname is derived from DHCP requests (as with dnsmasq),
## setting the hostname here will not make the machine reachable by this name.
## You may also wish to install avahi-daemon (see the "packages:" key below)
## to make your machine reachable by the .local domain
#hostname: ubuntu

## Set up the keyboard layout. See localectl(1), in particular the various
## list-x11-* sub-commands, to determine the available models, layouts,
## variants, and options
#keyboard:
#  model: pc105
#  layout: gb
#  variant:
#  options: ctrl:nocaps

# Enable password authentication with the SSH daemon
ssh_pwauth: true

## On first boot, use ssh-import-id to give the specific users SSH access to
## the default user
#ssh_import_id:
#- lp:my_launchpad_username
#- gh:my_github_username

## Add users and groups to the system, and import keys with the ssh-import-id
## utility
#groups:
#- robot: [robot]
#- robotics: [robot]
#- pi
#
#users:
#- default
#- name: robot
#  gecos: Mr. Robot
#  primary_group: robot
#  groups: users
#  ssh_import_id: foobar
#  lock_passwd: false
#  passwd: $5$hkui88$nvZgIle31cNpryjRfO9uArF7DYiBcWEnjqq7L1AQNN3

## Update apt database and upgrade packages on first boot
#package_update: true
#package_upgrade: true

## Install additional packages on first boot
#packages:
#- avahi-daemon
#- rng-tools
#- python3-gpiozero
#- [python3-serial, 3.5-1]

## Write arbitrary files to the file-system (including binaries!)
#write_files:
#- path: /etc/default/console-setup
#  content: |
#    # Consult the console-setup(5) manual page.
#    ACTIVE_CONSOLES="/dev/tty[1-6]"
#    CHARMAP="UTF-8"
#    VIDEOMODE=
#    FONT="Lat15-Terminus18x10.psf.gz"
#    FONTFACE=
#    FONTSIZE=
#    CODESET="Lat15"
#  permissions: '0644'
#  owner: root:root
#- encoding: gzip
#  path: /root/Makefile
#  content: !!binary |
#    H4sICF2DTWIAA01ha2VmaWxlAFNWCM8syVBILMjPyU/PTC1WKMlXiPB2dlFQNjSx5MpNteLi
#    dLDiSoRQxYl5KeWZyRkgXrSCkoqKRmaKgm6pppKCbmqhgoFCrIKamkK1QmpyRr6Ckn92YqWS
#    NdC80uQMBZhOa4VahZoaqIrwjMQSewXfxOxUhcwShcr80qLi1Jw0RSUuAIYfEJmVAAAA
#  owner: root:root
#  permissions: '0644'

## Run arbitrary commands at rc.local like time
#runcmd:
#- [ ls, -l, / ]
#- [ sh, -xc, "echo $(date) ': hello world!'" ]
#- [ wget, "http://ubuntu.com", -O, /run/mydir/index.html ]
