# This file contains a netplan-compatible configuration which cloud-init will
# apply on first-boot (note: it will *not* update the config after the first
# boot). Please refer to the cloud-init documentation and the netplan reference
# for full details:
#
# https://netplan.io/reference
# https://cloudinit.readthedocs.io/en/latest/topics/network-config.html
# https://cloudinit.readthedocs.io/en/latest/topics/network-config-format-v2.html
#
# Please note that the YAML format employed by this file is sensitive to
# differences in whitespace; if you are editing this file in an editor (like
# Notepad) which uses literal tabs, take care to only use spaces for
# indentation. See the following link for more details:
#
# https://en.wikipedia.org/wiki/YAML

# Some additional examples are commented out below

network:
  version: 2
  ethernets:
    zz-all-en:
      match:
        name: "en*"
      dhcp4: true
      optional: true
    zz-all-eth:
      match:
        name: "eth*"
      dhcp4: true
      optional: true

#  wifis:
#    wlan0:
#      dhcp4: true
#      optional: true
#      access-points:
#        myhomewifi:
#          password: "S3kr1t"
#        myworkwifi:
#          password: "correct battery horse staple"
#        workssid:
#          auth:
#            key-management: eap
#            method: peap
#            identity: "<EMAIL>"
#            password: "passw0rd"
#            ca-certificate: /etc/my_ca.pem

#      regulatory-domain: GB
