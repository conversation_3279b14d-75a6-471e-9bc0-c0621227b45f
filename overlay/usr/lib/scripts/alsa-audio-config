#!/usr/bin/env bash

# Check that we have the tools required available, otherwise exit with no error.
# Use command -v, not which, which is deprecated.
[[ -z $(command -v amixer) ]] && echo "Missing amixer; doing nothing." && exit 0
[[ -z $(command -v alsactl) ]] && echo "Missing alsactl; doing nothing." && exit 0
[[ -z $(command -v aplay) ]] && echo "Missing aplay; doing nothing." && exit 0

mixer_cmds() {
	parm=${3:-on}
	echo "sset '$1' '$2' '$parm'"
	echo "sset '$1' '$parm'"
}

if [ -f "$HOME/.config/sound.conf" ]; then
	alsactl restore -f "$HOME/.config/sound.conf"
else
	# get card num
	#card=`echo $1 | sed 's/[^0-9]*//g'`
	card1=$(aplay -l | grep "device 0" | awk '{print $3}')
	#echo $card

	for card in $card1; do (
		#echo $card

		# set common mixer params
		mixer_cmds Master 0db
		mixer_cmds Front 100%
		mixer_cmds PCM 0db
		mixer_cmds Synth 100%

		# mute CD, since using digital audio instead
		mixer_cmds CD 0% mute

		# Only unmute Line and Aux if they are possibly used.
		#  mixer_cmds Line 100%
		#  mixer_cmds Aux 100%

		# mute mic
		mixer_cmds Mic 0% mute

		# ESS 1969 chipset has 2 PCM channels
		mixer_cmds PCM,1 100%

		# Trident/YMFPCI/emu10k1
		mixer_cmds Wave 100%
		mixer_cmds Music 100%
		mixer_cmds AC97 100%
		mixer_cmds Surround 90%
		mixer_cmds 'Surround Digital' 90%
		mixer_cmds 'Wave Surround' 90%
		mixer_cmds 'Duplicate Front' 90%
		mixer_cmds 'Sigmatel 4-Speaker Stereo' 90%

		# CS4237B chipset:
		mixer_cmds 'Master Digital' 100%

		# DRC
		mixer_cmds 'Dynamic Range Compression' 90%

		# Envy24 chips with analog outs
		mixer_cmds DAC 100%
		mixer_cmds DAC,0 100%
		mixer_cmds DAC,1 100%

		# some notebooks use headphone instead of master
		mixer_cmds Headphone 100%
		mixer_cmds Speaker 100%
		mixer_cmds 'Internal Speaker' 0% mute
		mixer_cmds Playback 100%
		mixer_cmds Headphone 100%
		mixer_cmds Speaker 100%
		mixer_cmds Center 100%
		mixer_cmds LFE 100%
		mixer_cmds Center/LFE 100%

		# Intel P4P800-MX  (Ubuntu bug #5813)
		mixer_cmds 'Master Playback Switch' on

		# set digital output mixer params
		mixer_cmds 'IEC958' 100% on
		mixer_cmds 'IEC958 Output' 100%
		mixer_cmds 'IEC958 Coaxial' 100%
		mixer_cmds 'IEC958 LiveDrive' 100%
		mixer_cmds 'IEC958 Optical Raw' 100%
		mixer_cmds 'SPDIF Out' 100%
		mixer_cmds 'SPDIF Front' 100%
		mixer_cmds 'SPDIF Rear' 100%
		mixer_cmds 'SPDIF Center/LFE' 100%
		mixer_cmds 'Master Digital' 100%

		mixer_cmds 'Analog Front' 100%
		mixer_cmds 'Analog Rear' 100%
		mixer_cmds 'Analog Center/LFE' 100%

		# ASRock ION 330 (and perhaps others) has 2 IEC958 channels
		mixer_cmds IEC958,0 on
		mixer_cmds IEC958,1 on

		# some ION2 has much more IEC958 channels ...
		mixer_cmds IEC958,2 on
		mixer_cmds IEC958,3 on

		# ASRock ION 330 has Master Front set to 0
		mixer_cmds 'Master Front' 100%

		# Shuttle XS35GT needs this too
		mixer_cmds 'Master',0 100% on

		# and this for various Fusion devices like Zotac ZBOX
		mixer_cmds 'Front',0 100% on

		# NVidia CK804 sound devices
		mixer_cmds 'IEC958 Playback AC97-SPSA' 100%

		# Allwinner H3 Analog
		mixer_cmds 'Line Out' 0db on

		# Allwinner A20 Analog
		mixer_cmds 'Power Amplifier' 0db
		mixer_cmds 'Power Amplifier DAC' on
		mixer_cmds 'Power Amplifier Mute' on

		# Allwinner A64 Analog
		mixer_cmds Headphone 0db on
		mixer_cmds 'AIF1 Slot 0 Digital DAC' on

		# Amlogic G12 HDMI to PCM0
		mixer_cmds 'FRDDR_A SINK 1 SEL' 'OUT 1'
		mixer_cmds 'FRDDR_A SRC 1 EN' on
		mixer_cmds 'TDMOUT_B SRC SEL' 'IN 0'
		mixer_cmds 'TOHDMITX I2S SRC' 'I2S B'
		mixer_cmds 'TOHDMITX' on

		# Amlogic G12 S/PDIF to PCM1
		mixer_cmds 'FRDDR_B SINK 1 SEL' 'OUT 3'
		mixer_cmds 'FRDDR_B SRC 1 EN' on
		mixer_cmds 'SPDIFOUT SRC SEL' 'IN 1'
		mixer_cmds 'SPDIFOUT Playback' on

		# Amlogic GX HDMI and S/PDIF
		mixer_cmds 'AIU HDMI CTRL SRC' 'I2S'
		mixer_cmds 'AIU SPDIF SRC SEL' 'SPDIF'

		# RockPI 4B and ROCK 5 Analog
		mixer_cmds 'Right Headphone Mixer Right DAC' on
		mixer_cmds 'Left Headphone Mixer Left DAC' on

		# NanoPC T4 Analog
		mixer_cmds 'HPO L' on
		mixer_cmds 'HPO R' on
		mixer_cmds 'HPOVOL L' on
		mixer_cmds 'HPOVOL R' on
		mixer_cmds 'HPO MIX HPVOL' on
		mixer_cmds 'OUT MIXL DAC L1' on
		mixer_cmds 'OUT MIXR DAC R1' on
		mixer_cmds 'Stereo DAC MIXL DAC L1' on
		mixer_cmds 'Stereo DAC MIXR DAC R1' on
	) | amixer -c "$card" --stdin > /dev/null 2>&1; done
fi
