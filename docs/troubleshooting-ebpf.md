# eBPF 内核构建故障排除指南

本文档提供了在启用 eBPF 支持时可能遇到的常见问题及其解决方案。

## 常见构建错误

### 1. libelf 相关错误

**错误信息：**
```
No libelf found
make[6]: *** [Makefile:287: elfdep] Error 1
make[5]: *** [kernel/bpf/preload/Makefile:11: kernel/bpf/preload/libbpf.a] Error 2
```

**原因：** 缺少 libelf 开发库，这是编译 BPF 代码的必需依赖。

**解决方案：**
```bash
# 安装必需的开发库
sudo apt update
sudo apt install -y libelf-dev zlib1g-dev libbpf-dev

# 清理之前的构建
sudo rm -rf build/linux-rockchip/debian/build

# 重新构建
sudo SUITE=jammy ./scripts/build-kernel.sh
```

### 2. 交叉编译工具链错误

**错误信息：**
```
dpkg-architecture: warning: specified GNU system type aarch64-linux-gnu does not match CC system type x86_64-linux-gnu
```

**原因：** 缺少 ARM64 交叉编译工具链。

**解决方案：**
```bash
# 安装交叉编译工具链
sudo apt install -y gcc-aarch64-linux-gnu g++-aarch64-linux-gnu

# 验证安装
aarch64-linux-gnu-gcc --version
```

### 3. BTF 相关错误

**错误信息：**
```
BTF: .tmp_vmlinux.btf: pahole (pahole) is not available
Failed to generate BTF for vmlinux
```

**原因：** 缺少 pahole 工具，用于生成 BTF 调试信息。

**解决方案：**
```bash
# 安装 pahole 工具
sudo apt install -y dwarves

# 验证安装
pahole --version
```

### 4. 内存不足错误

**错误信息：**
```
virtual memory exhausted: Cannot allocate memory
make: *** [scripts/Makefile.build:xxx] Error 1
```

**原因：** 编译过程中内存不足。

**解决方案：**
```bash
# 检查可用内存
free -h

# 减少并行编译任务数
export MAKEFLAGS="-j$(nproc --ignore=2)"

# 或者增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 依赖检查脚本

使用以下脚本检查所有必需的依赖：

```bash
#!/bin/bash
# 检查 eBPF 构建依赖

echo "检查 eBPF 构建依赖..."

# 检查开发库
deps=("libelf-dev" "zlib1g-dev" "libbpf-dev" "dwarves" "gcc-aarch64-linux-gnu")
missing=()

for dep in "${deps[@]}"; do
    if ! dpkg -l | grep -q "^ii.*$dep"; then
        missing+=("$dep")
    fi
done

if [ ${#missing[@]} -gt 0 ]; then
    echo "缺少以下依赖："
    printf "  - %s\n" "${missing[@]}"
    echo ""
    echo "安装命令："
    echo "sudo apt update && sudo apt install -y ${missing[*]}"
else
    echo "✓ 所有依赖都已安装"
fi

# 检查工具
echo ""
echo "检查构建工具..."

tools=("aarch64-linux-gnu-gcc" "pahole" "make")
for tool in "${tools[@]}"; do
    if command -v "$tool" >/dev/null 2>&1; then
        echo "✓ $tool: $(command -v "$tool")"
    else
        echo "✗ $tool: 未找到"
    fi
done
```

## 配置验证

### 检查内核配置

```bash
# 检查当前内核配置中的 BPF 选项
grep -E "CONFIG_BPF|CONFIG_DEBUG_INFO_BTF" build/linux-rockchip/debian/build/build-rockchip/.config

# 应该看到类似输出：
# CONFIG_BPF=y
# CONFIG_BPF_SYSCALL=y
# CONFIG_BPF_JIT=y
# CONFIG_DEBUG_INFO_BTF=y
```

### 验证 eBPF 支持

在新内核启动后，验证 eBPF 支持：

```bash
# 检查 BTF 文件
ls -la /sys/kernel/btf/vmlinux

# 检查内核配置
zcat /proc/config.gz | grep -E "CONFIG_BPF|CONFIG_DEBUG_INFO_BTF"

# 测试基本 eBPF 功能
sudo bpftrace -e 'BEGIN { printf("eBPF is working!\n"); exit(); }'
```

## 性能优化

### 构建性能优化

```bash
# 使用更多 CPU 核心进行编译
export MAKEFLAGS="-j$(nproc)"

# 使用 ccache 加速重复编译
sudo apt install -y ccache
export PATH="/usr/lib/ccache:$PATH"
```

### 磁盘空间优化

```bash
# 清理旧的构建文件
sudo rm -rf build/linux-rockchip/debian/build

# 清理不需要的包缓存
sudo apt clean
sudo apt autoremove
```

## 日志分析

### 查看详细构建日志

```bash
# 查看最新的构建日志
tail -f build/logs/build-$(date +%Y%m%d)*.log

# 搜索错误信息
grep -i "error\|failed" build/logs/build-*.log

# 搜索 BPF 相关信息
grep -i "bpf\|btf" build/logs/build-*.log
```

### 常见日志模式

1. **成功的 BPF 编译：**
   ```
   Auto-detecting system features:
   ...                        libelf: [ on  ]
   ...                          zlib: [ on  ]
   ...                           bpf: [ on  ]
   ```

2. **失败的 BPF 编译：**
   ```
   Auto-detecting system features:
   ...                        libelf: [ OFF ]
   ...                          zlib: [ OFF ]
   ...                           bpf: [ on  ]
   
   No libelf found
   ```

## 环境特定问题

### Ubuntu 22.04 (Jammy)

```bash
# 确保使用正确的包源
sudo apt update
sudo apt install -y software-properties-common

# 可能需要启用 universe 仓库
sudo add-apt-repository universe
```

### 容器环境

```bash
# 在容器中可能需要额外的权限
docker run --privileged ...

# 或者挂载必要的目录
docker run -v /lib/modules:/lib/modules:ro ...
```

## 获取帮助

如果遇到未列出的问题：

1. **检查构建日志** - 查看 `build/logs/` 目录下的详细日志
2. **搜索错误信息** - 使用具体的错误信息在网上搜索
3. **检查内核版本兼容性** - 确保使用的内核版本支持所需的 eBPF 功能
4. **验证硬件支持** - 某些 eBPF 功能可能需要特定的硬件支持

## 相关资源

- [Linux 内核 BPF 文档](https://www.kernel.org/doc/html/latest/bpf/)
- [eBPF 官方网站](https://ebpf.io/)
- [Ubuntu 包搜索](https://packages.ubuntu.com/)
- [Rockchip 内核仓库](https://github.com/Joshua-Riek/linux-rockchip)
