# eBPF 内核配置启用脚本

本文档介绍如何使用 `scripts/enable-ebpf-kernel-config.sh` 脚本来为 Ubuntu Rockchip 内核启用 eBPF 支持。

## 概述

eBPF (extended Berkeley Packet Filter) 是一个强大的内核技术，允许在内核空间运行沙盒程序，无需修改内核源代码或加载内核模块。该脚本会自动配置所有必需的内核选项以启用完整的 eBPF 支持。

## 功能特性

- 自动检测和配置所有必需的 eBPF 内核选项
- 支持干运行模式，预览将要进行的更改
- 自动备份原始配置文件
- 详细的日志输出和进度报告
- 支持多个 Ubuntu 套件 (jammy, noble, oracular, plucky)

## 启用的内核选项

脚本会启用以下类别的内核配置选项：

### 核心 BPF 选项
- `CONFIG_BPF=y` - 基本 BPF 支持
- `CONFIG_BPF_JIT=y` - BPF JIT 编译器
- `CONFIG_BPF_JIT_DEFAULT_ON=y` - 默认启用 JIT
- `CONFIG_BPF_EVENTS=y` - BPF 事件支持
- `CONFIG_BPF_SYSCALL=y` - BPF 系统调用
- `CONFIG_HAVE_BPF_JIT=y` - 架构 BPF JIT 支持
- `CONFIG_HAVE_EBPF_JIT=y` - 扩展 BPF JIT 支持
- `CONFIG_FTRACE_SYSCALLS=y` - 系统调用跟踪

### BTF (BPF Type Format) 支持
- `CONFIG_DEBUG_INFO_BTF=y` - BTF 调试信息
- `CONFIG_DEBUG_INFO_BTF_MODULES=y` - 模块 BTF 支持

### 执行控制支持
- `CONFIG_BPF_KPROBE_OVERRIDE=y` - kprobe 覆盖支持

### CGROUP 和进程跟踪
- `CONFIG_CGROUPS=y` - 控制组支持
- `CONFIG_MEMCG=y` - 内存控制组
- `CONFIG_BLK_CGROUP=y` - 块 IO 控制器
- `CONFIG_CGROUP_SCHED=y` - 调度控制组
- `CONFIG_CGROUP_PIDS=y` - 进程控制组
- `CONFIG_CGROUP_FREEZER=y` - 冻结控制器
- `CONFIG_CPUSETS=y` - CPU 集合管理
- `CONFIG_PROC_PID_CPUSET=y` - 进程 CPU 集合信息
- `CONFIG_CGROUP_DEVICE=y` - 设备控制组
- `CONFIG_CGROUP_CPUACCT=y` - CPU 计费控制器
- `CONFIG_CGROUP_PERF=y` - 性能事件控制组
- `CONFIG_CGROUP_BPF=y` - 将 eBPF 程序附加到 cgroup

### 网络相关选项
- `CONFIG_NET_CLS_BPF=m` - BPF 分类器
- `CONFIG_NET_ACT_BPF=m` - BPF 动作
- `CONFIG_BPF_STREAM_PARSER=y` - BPF 流解析器
- `CONFIG_LWTUNNEL_BPF=y` - 轻量级隧道 BPF

## 使用方法

### 基本用法

```bash
# 设置 Ubuntu 套件并运行脚本
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh
```

### 命令行选项

```bash
# 显示帮助信息
./scripts/enable-ebpf-kernel-config.sh --help

# 干运行模式 - 仅显示将要进行的更改
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --dry-run

# 详细输出模式
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --verbose

# 指定特定的配置文件
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --config /path/to/.config

# 组合选项
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --dry-run --verbose
```

### 完整的工作流程

1. **准备内核源码**
   ```bash
   # 首先构建内核以获取源码
   sudo SUITE=jammy ./scripts/build-kernel.sh
   ```

2. **启用 eBPF 配置**
   ```bash
   # 先进行干运行检查
   sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --dry-run
   
   # 应用更改
   sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh
   ```

3. **重新构建内核**
   ```bash
   # 使用新配置重新构建内核
   sudo SUITE=jammy ./scripts/build-kernel.sh
   ```

4. **安装和重启**
   ```bash
   # 安装新的内核包
   sudo dpkg -i build/linux-*.deb
   
   # 重启系统
   sudo reboot
   ```

## 验证 eBPF 支持

重启后，可以通过以下方式验证 eBPF 支持：

```bash
# 检查 BTF 文件是否存在
ls -la /sys/kernel/btf/vmlinux

# 检查内核配置
zcat /proc/config.gz | grep -E "CONFIG_BPF|CONFIG_DEBUG_INFO_BTF"

# 使用 bpftool 检查 BPF 功能（如果已安装）
sudo bpftool feature

# 测试基本的 BPF 功能
sudo bpftrace -e 'BEGIN { printf("eBPF is working!\n"); exit(); }'
```

## 故障排除

### 常见问题

1. **找不到内核源码目录**
   - 确保先运行了 `build-kernel.sh` 脚本来获取内核源码

2. **找不到配置文件**
   - 使用 `--config` 选项指定正确的配置文件路径

3. **权限错误**
   - 确保以 root 权限运行脚本

4. **SUITE 未设置**
   - 设置正确的 SUITE 环境变量（jammy, noble, oracular, plucky）

### 恢复原始配置

如果需要恢复原始配置，可以使用自动创建的备份文件：

```bash
# 查找备份文件
ls -la build/linux-rockchip/.config.backup.*

# 恢复备份
cp build/linux-rockchip/.config.backup.YYYYMMDD_HHMMSS build/linux-rockchip/.config
```

## 支持的套件

- Ubuntu 22.04 LTS (Jammy Jellyfish) - `SUITE=jammy`
- Ubuntu 24.04 LTS (Noble Numbat) - `SUITE=noble`
- Ubuntu 24.10 (Oracular Oriole) - `SUITE=oracular`
- Ubuntu 25.04 (Plucky Puffin) - `SUITE=plucky`

## 注意事项

- 该脚本会自动备份原始配置文件
- 某些选项可能在较旧的内核版本中不可用
- 建议在生产环境使用前先在测试环境中验证
- eBPF 功能需要相对较新的内核版本（建议 4.19+）

## 相关资源

- [eBPF 官方文档](https://ebpf.io/)
- [Linux 内核 BPF 文档](https://www.kernel.org/doc/html/latest/bpf/)
- [Cilium eBPF 库](https://github.com/cilium/ebpf)
- [bpftrace 工具](https://github.com/iovisor/bpftrace)
