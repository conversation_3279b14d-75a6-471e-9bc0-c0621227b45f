# eBPF 内核配置支持

本项目新增了独立的 eBPF 内核配置脚本，用于为 Ubuntu Rockchip 内核启用完整的 eBPF 支持。

## 新增文件

### 1. 主要脚本
- **`scripts/enable-ebpf-kernel-config.sh`** - 主要的 eBPF 配置脚本
  - 自动检测和配置所有必需的 eBPF 内核选项
  - 支持干运行模式和详细输出
  - 自动备份原始配置文件
  - 支持多个 Ubuntu 套件

### 2. 文档
- **`docs/enable-ebpf.md`** - 详细的使用文档和配置说明
- **`README-eBPF.md`** - 本文件，功能概述

### 3. 测试和集成脚本
- **`test-ebpf-config.sh`** - 测试和演示脚本
  - 检查当前内核配置状态
  - 显示已有的 eBPF 相关选项
  - 提供使用指导

- **`build-with-ebpf.sh`** - 集成构建脚本
  - 将 eBPF 配置集成到完整构建流程
  - 支持仅配置 eBPF 或完整构建
  - 提供干运行和详细输出模式

## 快速开始

### 1. 检查当前状态
```bash
# 运行测试脚本查看当前配置
./test-ebpf-config.sh
```

### 2. 启用 eBPF 配置
```bash
# 干运行模式 - 查看将要进行的更改
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --dry-run --verbose

# 应用更改
sudo SUITE=jammy ./scripts/enable-ebpf-kernel-config.sh --verbose
```

### 3. 重新构建内核
```bash
# 使用新配置重新构建内核
sudo SUITE=jammy ./scripts/build-kernel.sh
```

### 4. 使用集成构建脚本（推荐）
```bash
# 一键构建带 eBPF 支持的完整系统
sudo ./build-with-ebpf.sh -s jammy -b rock-5b -f server

# 仅配置 eBPF（干运行）
sudo ./build-with-ebpf.sh --ebpf-only -s jammy --dry-run

# 详细输出模式
sudo ./build-with-ebpf.sh -s jammy -b orangepi-5 -f desktop --verbose
```

## 支持的 eBPF 功能

脚本会启用以下 eBPF 功能类别：

### 核心 BPF 支持
- 基本 BPF 功能 (`CONFIG_BPF`)
- BPF JIT 编译器 (`CONFIG_BPF_JIT`)
- BPF 系统调用 (`CONFIG_BPF_SYSCALL`)
- BPF 事件支持 (`CONFIG_BPF_EVENTS`)

### BTF (BPF Type Format) 支持
- BTF 调试信息 (`CONFIG_DEBUG_INFO_BTF`)
- 模块 BTF 支持 (`CONFIG_DEBUG_INFO_BTF_MODULES`)

### 执行控制
- kprobe 覆盖支持 (`CONFIG_BPF_KPROBE_OVERRIDE`)

### CGROUP 集成
- 完整的 CGROUP 支持
- BPF 程序附加到 cgroup (`CONFIG_CGROUP_BPF`)
- 进程和资源跟踪

### 网络功能
- BPF 网络分类器和动作
- 流解析器支持
- 轻量级隧道支持

## 脚本特性

### 智能配置检测
- 自动检测现有的内核配置文件
- 识别已启用的选项，避免重复配置
- 显示需要更改的选项

### 安全操作
- 自动备份原始配置文件
- 干运行模式预览更改
- 详细的操作日志

### 灵活使用
- 支持多个 Ubuntu 套件 (jammy, noble, oracular, plucky)
- 可指定自定义配置文件
- 详细和简洁输出模式

## 验证 eBPF 支持

重新构建并安装内核后，可以通过以下方式验证 eBPF 支持：

```bash
# 检查 BTF 文件
ls -la /sys/kernel/btf/vmlinux

# 检查内核配置
zcat /proc/config.gz | grep -E 'CONFIG_BPF|CONFIG_DEBUG_INFO_BTF'

# 测试基本 eBPF 功能（需要安装 bpftrace）
sudo bpftrace -e 'BEGIN { printf("eBPF is working!\n"); exit(); }'
```

## 使用场景

这个 eBPF 配置脚本适用于以下场景：

### 1. 系统监控和可观测性
- 使用 bpftrace、bcc 等工具进行系统跟踪
- 性能分析和调试
- 网络流量监控

### 2. 安全和合规
- 运行时安全监控
- 系统调用跟踪
- 进程行为分析

### 3. 网络功能
- 高性能网络处理
- 负载均衡
- 流量控制和 QoS

### 4. 容器和云原生
- Kubernetes 网络策略
- 服务网格数据平面
- 容器运行时安全

## 兼容性

### 支持的套件
- Ubuntu 22.04 LTS (Jammy) - `SUITE=jammy`
- Ubuntu 24.04 LTS (Noble) - `SUITE=noble`
- Ubuntu 24.10 (Oracular) - `SUITE=oracular`
- Ubuntu 25.04 (Plucky) - `SUITE=plucky`

### 内核版本要求
- 建议使用 Linux 4.19 或更新版本
- 某些高级功能可能需要更新的内核版本

## 故障排除

### 常见问题
1. **权限错误** - 确保以 root 权限运行脚本
2. **找不到内核源码** - 先运行 `build-kernel.sh` 获取源码
3. **配置文件不存在** - 使用 `--config` 指定正确路径

### 恢复配置
脚本会自动创建备份文件，可以通过以下方式恢复：
```bash
# 查找备份文件
ls -la build/linux-rockchip/.config.backup.*

# 恢复备份
cp build/linux-rockchip/.config.backup.YYYYMMDD_HHMMSS build/linux-rockchip/.config
```

## 贡献

如果发现问题或有改进建议，请：
1. 检查现有的 issue
2. 提交详细的问题报告
3. 提供复现步骤和环境信息

## 相关资源

- [eBPF 官方文档](https://ebpf.io/)
- [Linux 内核 BPF 文档](https://www.kernel.org/doc/html/latest/bpf/)
- [bpftrace 项目](https://github.com/iovisor/bpftrace)
- [Cilium eBPF 库](https://github.com/cilium/ebpf)
